#!/bin/bash
# 一键部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，实现完整的自动化部署流程

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ORIGINAL_SCRIPT_DIR="$SCRIPT_DIR"
echo "$SCRIPT_DIR"
source "$SCRIPT_DIR/lib/common.sh"
SCRIPT_DIR="$ORIGINAL_SCRIPT_DIR"
echo "$SCRIPT_DIR"
# 脚本信息
SCRIPT_NAME="一键部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 全局变量
# =============================================================================

# 部署状态跟踪
declare -A DEPLOYMENT_STATUS=(
    ["infrastructure"]="pending"
    ["mongodb"]="pending"
    ["redis"]="pending"
    ["kafka"]="pending"
    ["tdengine"]="pending"
    ["nebula"]="pending"
    ["dameng"]="pending"
    ["monitoring"]="pending"
)

# 部署开始时间
DEPLOYMENT_START_TIME=$(date +%s)

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_INFRASTRUCTURE=false
SKIP_DATABASES=true
SKIP_CACHE=false
SKIP_MESSAGING=false
SKIP_SPECIALIZED=true
SKIP_MONITORING=false
FORCE_REINSTALL=false
DRY_RUN=false
PARALLEL_DEPLOY=false
ROLLBACK_ON_FAILURE=true

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --offline)
            export OFFLINE_MODE="true"
            export INTERNET_ACCESS="false"
            export USE_LOCAL_REPO="true"
            log_info "启用离线部署模式"
            shift
            ;;
        --online)
            export OFFLINE_MODE="false"
            export INTERNET_ACCESS="true"
            export USE_LOCAL_REPO="false"
            log_info "启用在线部署模式"
            shift
            ;;
        --skip-infrastructure)
            SKIP_INFRASTRUCTURE=true
            shift
            ;;
        --skip-databases)
            SKIP_DATABASES=true
            shift
            ;;
        --skip-cache)
            SKIP_CACHE=true
            shift
            ;;
        --skip-messaging)
            SKIP_MESSAGING=true
            shift
            ;;
        --skip-specialized)
            SKIP_SPECIALIZED=true
            shift
            ;;
        --skip-monitoring)
            SKIP_MONITORING=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --parallel)
            PARALLEL_DEPLOY=true
            shift
            ;;
        --no-rollback)
            ROLLBACK_ON_FAILURE=false
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --offline               启用离线部署模式"
            echo "  --online                启用在线部署模式"
            echo "  --skip-infrastructure   跳过基础设施部署"
            echo "  --skip-databases        跳过数据库部署"
            echo "  --skip-cache            跳过缓存服务部署"
            echo "  --skip-messaging        跳过消息队列部署"
            echo "  --skip-monitoring       跳过监控系统部署"
            echo "  --force-reinstall       强制重新安装"
            echo "  --dry-run               仅显示将要执行的操作"
            echo "  --parallel              并行部署（实验性）"
            echo "  --no-rollback           失败时不自动回滚"
            echo "  -h, --help              显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# 部署前检查函数
# =============================================================================

pre_deployment_checks() {
    log_info "执行部署前检查..."
    
    # 检查配置文件
    if [[ ! -f "$CONFIG_DIR/global.conf" ]]; then
        log_error "全局配置文件不存在: $CONFIG_DIR/global.conf"
        return 1
    fi
    
    if [[ ! -f "$CONFIG_DIR/hosts.conf" ]]; then
        log_error "主机配置文件不存在: $CONFIG_DIR/hosts.conf"
        return 1
    fi

    if [[ ! -d "$OFFLINE_PREP_DIR" ]]; then
        mkdir -p "$OFFLINE_PREP_DIR"
        mv ../../offline-prep/* "$OFFLINE_PREP_DIR"
#        log_error "软件仓库目录不存在: $SOFTWARE_REPO"
#        return 1
    fi

    # 检查软件仓库
    if [[ ! -d "$SOFTWARE_REPO" ]]; then
        mkdir -p "$SOFTWARE_REPO"
        mv ../../software-repo/* "$SOFTWARE_REPO"
#        log_error "软件仓库目录不存在: $SOFTWARE_REPO"
#        return 1
    fi

    if [ -z "$(ls -A $SOFTWARE_REPO)" ]; then
        log_info "软件仓库目录为空,移动软件文件到指定目录"
        mv ../../software-repo/* "$SOFTWARE_REPO"
    else
        log_info "软件仓库目录已存在"
    fi

    # 检查必需的脚本
    local required_scripts=(
        "prepare_environment.sh"
        "distribute_packages.sh"
        "deploy_mongodb.sh"
        "deploy_redis.sh"
        "deploy_kafka.sh"
        "deploy_tdengine.sh"
        "deploy_nebula.sh"
        "deploy_dameng.sh"
        "deploy_monitoring.sh"
    )
    echo "$SCRIPT_DIR"    
    for script in "${required_scripts[@]}"; do
        if [[ ! -f "$SCRIPT_DIR/$script" ]]; then
            log_error "必需的脚本不存在: $SCRIPT_DIR/$script"
            return 1
        fi
        
        if [[ ! -x "$SCRIPT_DIR/$script" ]]; then
            log_warn "脚本没有执行权限，正在设置: $script"
            chmod +x "$SCRIPT_DIR/$script"
        fi
    done
    
    # 检查SSH连接
    log_info "检查SSH连接..."
    local all_hosts
    mapfile -t all_hosts < <(get_all_hosts)
    
    local failed_hosts=()
    for host in "${all_hosts[@]}"; do
        if ! check_ssh_connection "$host" "root"; then
            failed_hosts+=("$host")
        fi
    done
    
    if [[ ${#failed_hosts[@]} -gt 0 ]]; then
        log_error "以下主机SSH连接失败: ${failed_hosts[*]}"
        return 1
    fi
    
    log_info "部署前检查通过"
    return 0
}

# =============================================================================
# 部署状态管理函数
# =============================================================================

update_deployment_status() {
    local component=$1
    local status=$2
    
    DEPLOYMENT_STATUS["$component"]="$status"
    log_info "更新部署状态: $component -> $status"
}

get_deployment_status() {
    local component=$1
    echo "${DEPLOYMENT_STATUS[$component]}"
}

print_deployment_summary() {
    local current_time=$(date +%s)
    local elapsed_time=$((current_time - DEPLOYMENT_START_TIME))
    local elapsed_minutes=$((elapsed_time / 60))
    local elapsed_seconds=$((elapsed_time % 60))
    
    echo
    echo "=== 部署状态摘要 ==="
    echo "部署时间: ${elapsed_minutes}分${elapsed_seconds}秒"
    echo
    
    for component in "${!DEPLOYMENT_STATUS[@]}"; do
        local status="${DEPLOYMENT_STATUS[$component]}"
        local status_icon
        
        case $status in
            "success") status_icon="✓" ;;
            "failed") status_icon="✗" ;;
            "running") status_icon="⟳" ;;
            "skipped") status_icon="⊘" ;;
            *) status_icon="○" ;;
        esac
        
        printf "%-15s: %s %s\n" "$component" "$status_icon" "$status"
    done
    
    echo "===================="
}

# =============================================================================
# 部署函数
# =============================================================================

deploy_infrastructure() {
    if [[ "$SKIP_INFRASTRUCTURE" == "true" ]]; then
        update_deployment_status "infrastructure" "skipped"
        return 0
    fi
    
    log_info "开始部署基础设施..."
    update_deployment_status "infrastructure" "running"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将执行基础设施部署"
        update_deployment_status "infrastructure" "success"
        return 0
    fi
    
    # 环境准备
    if ! "$SCRIPT_DIR/prepare_environment.sh"; then
        log_error "环境准备失败"
        update_deployment_status "infrastructure" "failed"
        return 1
    fi
    
    # 分发安装包
    if ! "$SCRIPT_DIR/distribute_packages.sh"; then
        log_error "安装包分发失败"
        update_deployment_status "infrastructure" "failed"
        return 1
    fi
    
    update_deployment_status "infrastructure" "success"
    log_info "基础设施部署完成"
    return 0
}

deploy_databases() {
    if [[ "$SKIP_DATABASES" == "true" ]]; then
        update_deployment_status "mongodb" "skipped"
        update_deployment_status "dameng" "skipped"
        return 0
    fi
    
#    log_info "开始部署数据库服务..."
    
    # 部署达梦数据库（核心业务数据库，优先部署）
#    update_deployment_status "dameng" "running"
#    if [[ "$DRY_RUN" == "true" ]]; then
#        log_info "[DRY RUN] 将部署达梦数据库"
#        update_deployment_status "dameng" "success"
#    else
#        if "$SCRIPT_DIR/deploy_dameng.sh"; then
#            update_deployment_status "dameng" "success"
#            log_info "达梦数据库部署完成"
#        else
#            log_error "达梦数据库部署失败"
#            update_deployment_status "dameng" "failed"
#            return 1
#        fi
#    fi
    
    # 部署MongoDB
    update_deployment_status "mongodb" "running"
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将部署MongoDB"
        update_deployment_status "mongodb" "success"
    else
        if "$SCRIPT_DIR/deploy_mongodb.sh"; then
            update_deployment_status "mongodb" "success"
            log_info "MongoDB部署完成"
        else
            log_error "MongoDB部署失败"
            update_deployment_status "mongodb" "failed"
            return 1
        fi
    fi
    
    log_info "数据库服务部署完成"
    return 0
}

deploy_cache_services() {
    if [[ "$SKIP_CACHE" == "true" ]]; then
        update_deployment_status "redis" "skipped"
        return 0
    fi
    
    log_info "开始部署缓存服务..."
    update_deployment_status "redis" "running"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将部署Redis集群"
        update_deployment_status "redis" "success"
        return 0
    fi
    
    if "$SCRIPT_DIR/deploy_redis.sh"; then
        update_deployment_status "redis" "success"
        log_info "Redis集群部署完成"
    else
        log_error "Redis集群部署失败"
        update_deployment_status "redis" "failed"
        return 1
    fi
    
    return 0
}

deploy_messaging_services() {
    if [[ "$SKIP_MESSAGING" == "true" ]]; then
        update_deployment_status "kafka" "skipped"
        return 0
    fi
    
    log_info "开始部署消息队列服务..."
    update_deployment_status "kafka" "running"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将部署Kafka集群"
        update_deployment_status "kafka" "success"
        return 0
    fi
    
    if "$SCRIPT_DIR/deploy_kafka.sh"; then
        update_deployment_status "kafka" "success"
        log_info "Kafka集群部署完成"
    else
        log_error "Kafka集群部署失败"
        update_deployment_status "kafka" "failed"
        return 1
    fi
    
    return 0
}

deploy_specialized_databases() {

    if [[ "$SKIP_SPECIALIZED" == "true" ]]; then
          update_deployment_status "tdengine" "skipped"
          update_deployment_status "nebula" "skipped"
          return 0
    fi
    log_info "开始部署专业数据库服务..."
    
    # 部署TDEngine
    update_deployment_status "tdengine" "running"
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将部署TDEngine"
        update_deployment_status "tdengine" "success"
    else
        if "$SCRIPT_DIR/deploy_tdengine.sh"; then
            update_deployment_status "tdengine" "success"
            log_info "TDEngine部署完成"
        else
            log_error "TDEngine部署失败"
            update_deployment_status "tdengine" "failed"
            return 1
        fi
    fi
    
    # 部署NebulaGraph
    update_deployment_status "nebula" "running"
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将部署NebulaGraph"
        update_deployment_status "nebula" "success"
    else
        if "$SCRIPT_DIR/deploy_nebula.sh"; then
            update_deployment_status "nebula" "success"
            log_info "NebulaGraph部署完成"
        else
            log_error "NebulaGraph部署失败"
            update_deployment_status "nebula" "failed"
            return 1
        fi
    fi
    
    log_info "专业数据库服务部署完成"
    return 0
}

deploy_monitoring_services() {
    if [[ "$SKIP_MONITORING" == "true" ]]; then
        update_deployment_status "monitoring" "skipped"
        return 0
    fi
    
    log_info "开始部署监控服务..."
    update_deployment_status "monitoring" "running"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将部署监控系统"
        update_deployment_status "monitoring" "success"
        return 0
    fi
    
    if "$SCRIPT_DIR/deploy_monitoring.sh"; then
        update_deployment_status "monitoring" "success"
        log_info "监控系统部署完成"
    else
        log_error "监控系统部署失败"
        update_deployment_status "monitoring" "failed"
        return 1
    fi
    
    return 0
}

# =============================================================================
# 回滚函数
# =============================================================================

rollback_deployment() {
    log_warn "开始执行部署回滚..."
    
    # 这里应该实现具体的回滚逻辑
    # 由于回滚逻辑复杂，这里只是框架
    
    log_warn "回滚功能正在开发中，请手动清理失败的部署"
    return 0
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始一键部署..."
    
    # 获取锁
    if ! acquire_lock "deploy_all"; then
        log_error "无法获取锁，可能有其他部署实例正在运行"
        exit 1
    fi

    # 部署前检查
    if ! pre_deployment_checks; then
        log_error "部署前检查失败"
        exit 1
    fi

    # 离线环境检查
    if [[ "$OFFLINE_MODE" == "true" ]]; then
        log_info "检查离线部署环境..."
        if [[ -f "$CONFIG_DIR/offline.conf" ]]; then
            source "$CONFIG_DIR/offline.conf"
        fi

        # 验证离线环境
        if ! check_offline_environment "localhost"; then
            log_error "离线环境检查失败"
            exit 1
        fi

        log_info "离线环境检查通过"
    fi


    
    # 输出部署计划
    echo
    echo "=== 部署计划 ==="
    echo "部署模式: $([ "$OFFLINE_MODE" == "true" ] && echo "离线模式" || echo "在线模式")"
    echo "基础设施: $([ "$SKIP_INFRASTRUCTURE" == "true" ] && echo "跳过" || echo "部署")"
    echo "数据库服务: $([ "$SKIP_DATABASES" == "true" ] && echo "跳过" || echo "部署")"
    echo "缓存服务: $([ "$SKIP_CACHE" == "true" ] && echo "跳过" || echo "部署")"
    echo "消息队列: $([ "$SKIP_MESSAGING" == "true" ] && echo "跳过" || echo "部署")"
    echo "监控系统: $([ "$SKIP_MONITORING" == "true" ] && echo "跳过" || echo "部署")"
    echo "专业数据库: $([ "$SKIP_SPECIALIZED" == "true" ] && echo "跳过" || echo "部署")"
    echo "================"
    echo
    
    if [[ "$DRY_RUN" != "true" ]]; then
        read -p "确认开始部署？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "用户取消部署"
            exit 0
        fi
    fi
    
    # 执行部署
    local deployment_failed=false
    
    # 1. 基础设施部署
    if ! deploy_infrastructure; then
        deployment_failed=true
    fi
    
    # 2. 数据库服务部署
    if [[ "$deployment_failed" != "true" ]] && ! deploy_databases; then
        deployment_failed=true
    fi
    
    # 3. 缓存服务部署
    if [[ "$deployment_failed" != "true" ]] && ! deploy_cache_services; then
        deployment_failed=true
    fi
    
    # 4. 消息队列部署
    if [[ "$deployment_failed" != "true" ]] && ! deploy_messaging_services; then
        deployment_failed=true
    fi
    
    # 5. 专业数据库部署
    if [[ "$deployment_failed" != "true" ]] && ! deploy_specialized_databases; then
        deployment_failed=true
    fi
    
    # 6. 监控系统部署
    if [[ "$deployment_failed" != "true" ]] && ! deploy_monitoring_services; then
        deployment_failed=true
    fi
    
    # 输出部署结果
    print_deployment_summary
    
    if [[ "$deployment_failed" == "true" ]]; then
        log_error "部署过程中发生错误"
        
        if [[ "$ROLLBACK_ON_FAILURE" == "true" && "$DRY_RUN" != "true" ]]; then
            rollback_deployment
        fi
        
        exit 1
    else
        log_info "所有服务部署完成！"
        
        # 输出访问信息
        echo
        echo "=== 服务访问信息 ==="
        echo "MongoDB: mongodb://192.168.1.101:27017,192.168.1.102:27017,192.168.1.103:27017"
        echo "Redis: 192.168.1.111:7001,192.168.1.112:7002,192.168.1.113:7003"
        echo "Kafka: 192.168.1.121:9092,192.168.1.122:9092,192.168.1.123:9092"
        echo "TDEngine: 192.168.1.131:6030"
        echo "NebulaGraph: 192.168.1.141:9669"
        echo "达梦数据库: 192.168.1.151:5236"
        echo "Grafana: http://192.168.1.202:3000"
        echo "Prometheus: http://192.168.1.201:9090"
        echo "====================="
        
        exit 0
    fi
}

# 设置脚本权限
chmod +x "$0"

# 执行主函数
main "$@"
