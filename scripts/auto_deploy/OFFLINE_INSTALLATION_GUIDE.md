# 离线安装指南

> **工业场景基础服务自动化部署 - 离线安装模式**

## 📋 概述

本指南详细说明如何在无法访问外网的工业环境中进行基础服务的离线部署。离线模式是专门为工业场景设计的部署方式，确保在完全隔离的网络环境中也能成功部署所有基础服务。

## 🎯 离线模式特性

### ✅ 完全离线支持
- **无需外网访问**：所有软件包和依赖都从本地获取
- **本地软件仓库**：使用预先准备的YUM和PIP仓库
- **预编译包支持**：针对海光CPU优化的预编译包
- **本地时间同步**：使用本地时钟，无需外部NTP服务器

### ✅ 智能包管理
- **自动检测**：自动检测并使用可用的安装包
- **多格式支持**：支持源码包、预编译包、RPM包等多种格式
- **依赖解析**：完整的离线依赖解析和安装

### ✅ 安全可靠
- **数字签名验证**：支持离线包完整性验证
- **本地证书**：自动生成和管理本地SSL证书
- **安全配置**：针对离线环境的安全配置优化

## 🚀 快速开始

### 1. 准备离线安装包

在有网络的环境中运行：

```bash
# 下载并准备离线安装包
./prepare_offline_packages.sh

# 这将创建包含所有必需软件的离线包
# industrial-infrastructure-offline-YYYYMMDD.tar.gz
```

### 2. 传输到目标环境

```bash
# 将离线包传输到目标环境
scp industrial-infrastructure-offline-*.tar.gz target-server:/apps/

# 在目标服务器上解压
tar -xzf industrial-infrastructure-offline-*.tar.gz -C /apps/
```

### 3. 配置离线模式

```bash
# 编辑全局配置文件
vim scripts/auto_deploy/config/global.conf

# 设置离线模式
export OFFLINE_MODE="true"
export INTERNET_ACCESS="false"
export USE_LOCAL_REPO="true"
export LOCAL_YUM_REPO="/apps/offline-prep/yum-repo"
export LOCAL_PIP_REPO="/apps/offline-prep/pip-repo"
export SOFTWARE_REPO="/apps/offline-prep/software"
```

### 4. 执行离线部署

```bash
# 方式1：使用命令行参数
./deploy_all.sh --offline

# 方式2：使用配置文件
./deploy_all.sh

# 方式3：分步骤部署
./deploy_infrastructure.sh --offline
./deploy_dameng.sh --offline
./deploy_mongodb.sh --offline
# ... 其他服务
```

## 📦 离线包结构

```
offline-prep/
├── yum-repo/                 # YUM软件仓库
│   ├── base/                 # 基础系统包
│   ├── epel/                 # EPEL扩展包
│   └── extras/               # 额外软件包
├── pip-repo/                 # Python包仓库
│   ├── simple/               # 简单索引
│   └── *.whl, *.tar.gz      # Python包文件
└── software/                 # 应用软件包
    ├── mongodb/              # MongoDB相关包
    ├── redis/                # Redis相关包
    ├── kafka/                # Kafka相关包
    ├── tdengine/             # TDEngine相关包
    ├── nebula/               # NebulaGraph相关包
    ├── dameng/               # 达梦数据库相关包
    ├── monitoring/           # 监控系统相关包
    └── jdk/                  # JDK相关包
```

## 🔧 软件包要求

### 必需的软件包

#### MongoDB
- `mongodb-hygon-precompiled.tar.gz` (推荐，海光CPU优化)
- 或 `mongodb-src-r4.4.18.tar.gz` (源码包)

#### Redis
- `redis-hygon-precompiled.tar.gz` (推荐，海光CPU优化)
- 或 `redis-7.0.8.tar.gz` (源码包)

#### Kafka
- `kafka_2.13-3.4.0.tgz` (推荐)
- 或 `apache-kafka-3.4.0.tar.gz`

#### JDK
- `bisheng-jdk-11.0.19-linux-x64.tar.gz` (推荐，华为毕昇JDK)
- 或 `openjdk-11.0.19_linux-x64_bin.tar.gz`

#### TDEngine
- `TDengine-server-3.0.4.0.tar.gz`
- 或 `TDengine-server-3.0.4.0.rpm`

#### NebulaGraph
- `nebula-graph-3.5.0.tar.gz`

#### 达梦数据库
- `dm8_20230808_x86_rh6_64.iso`
- 或 `dm8_20230808_x86_rh6_64.tar.gz`

#### 监控系统
- `prometheus-2.45.0.linux-amd64.tar.gz`
- `grafana-10.0.0.linux-amd64.tar.gz`
- `alertmanager-0.25.0.linux-amd64.tar.gz`

## ⚙️ 离线配置详解

### 全局配置 (global.conf)

```bash
# 离线模式开关
export OFFLINE_MODE="true"
export INTERNET_ACCESS="false"
export USE_LOCAL_REPO="true"

# 离线资源路径
export LOCAL_YUM_REPO="/apps/offline-prep/yum-repo"
export LOCAL_PIP_REPO="/apps/offline-prep/pip-repo"
export OFFLINE_PACKAGE_DIR="/apps/offline-prep/software"
```

### 网络配置 (network.conf)

```bash
# 离线环境DNS配置
export DNS_SERVERS="127.0.0.1"  # 使用本地DNS
export NTP_SERVERS=""           # 不使用外部NTP

# 时间同步配置
export USE_LOCAL_TIME="true"
export NTP_STRATUM="8"
```

### 服务配置

每个服务都支持离线配置，例如：

```bash
# MongoDB离线配置
export MONGODB_USE_PRECOMPILED="true"
export MONGODB_OFFLINE_PACKAGE="mongodb-hygon-precompiled.tar.gz"

# Redis离线配置
export REDIS_USE_PRECOMPILED="true"
export REDIS_OFFLINE_PACKAGE="redis-hygon-precompiled.tar.gz"
```

## 🔍 离线部署验证

### 1. 环境检查

```bash
# 检查离线环境
./scripts/auto_deploy/lib/common.sh
check_offline_environment localhost
```

### 2. 软件包验证

```bash
# 验证软件包完整性
find /apps/offline-prep -name "*.tar.gz" -exec ls -lh {} \;
find /apps/offline-prep -name "*.rpm" -exec rpm -qp {} \;
```

### 3. 服务验证

```bash
# 验证服务状态
systemctl status mongodb
systemctl status redis-7001
systemctl status kafka
systemctl status taosd
systemctl status nebula-metad
systemctl status dmserver
systemctl status prometheus
```

## 🚨 故障排除

### 常见问题

#### 1. 软件包缺失
```bash
错误: 找不到MongoDB源码包或预编译包
解决: 确保软件包已正确放置在指定目录
```

#### 2. YUM仓库配置失败
```bash
错误: 离线模式下本地YUM仓库不存在
解决: 检查LOCAL_YUM_REPO路径是否正确
```

#### 3. Python包安装失败
```bash
错误: 离线模式下本地PIP仓库不存在
解决: 检查LOCAL_PIP_REPO路径是否正确
```

#### 4. 时间同步问题
```bash
问题: 离线环境时间不同步
解决: 配置本地时钟同步或手动设置时间
```

### 调试模式

```bash
# 启用详细日志
export LOG_LEVEL="DEBUG"
export VERBOSE_LOGGING="true"

# 干运行模式
./deploy_all.sh --offline --dry-run

# 单步调试
./deploy_mongodb.sh --offline --dry-run
```

## 📊 性能优化

### 离线环境优化建议

1. **使用预编译包**：优先使用针对海光CPU优化的预编译包
2. **本地缓存**：启用本地缓存以提高性能
3. **并行部署**：在资源充足时使用并行部署
4. **存储优化**：使用SSD存储提高I/O性能

### 资源要求

- **磁盘空间**：至少50GB用于离线包和数据
- **内存**：每台服务器至少32GB RAM
- **CPU**：推荐海光CPU，至少8核心
- **网络**：内网带宽至少1Gbps

## 🔒 安全考虑

### 离线环境安全

1. **包完整性**：验证所有软件包的数字签名
2. **本地证书**：使用自签名证书进行内部通信
3. **访问控制**：严格的用户权限和访问控制
4. **审计日志**：完整的部署和操作日志记录

### 安全配置

```bash
# 启用包验证
export VERIFY_PACKAGES="true"

# 使用自签名证书
export USE_SELF_SIGNED_CERTS="true"
export CERT_VALIDITY_DAYS="3650"

# 启用审计日志
export AUDIT_LOGGING="true"
export AUDIT_LOG_PATH="/var/log/deploy-audit.log"
```

## 📝 最佳实践

### 1. 离线包准备
- 在与目标环境相同的操作系统上准备离线包
- 定期更新离线包以获取安全补丁
- 验证所有软件包的完整性和兼容性

### 2. 部署规划
- 制定详细的部署计划和回滚策略
- 在测试环境中验证离线部署流程
- 准备充足的存储空间和备份策略

### 3. 运维管理
- 建立本地软件仓库的更新机制
- 定期备份配置和数据
- 监控系统资源使用情况

---

**离线部署模式为工业场景提供了完全自主可控的基础服务部署解决方案，确保在任何网络环境下都能可靠部署和运行。**
