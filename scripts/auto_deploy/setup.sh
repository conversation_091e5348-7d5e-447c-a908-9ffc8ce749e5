#!/bin/bash
# 自动化部署系统初始化脚本
# 设置脚本权限和初始化环境

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "=== 工业场景基础服务自动化部署系统初始化 ==="
echo "脚本目录: $SCRIPT_DIR"
echo

# =============================================================================
# 设置脚本权限
# =============================================================================

echo "设置脚本执行权限..."

# 主要脚本
chmod +x "$SCRIPT_DIR/deploy_all.sh"
chmod +x "$SCRIPT_DIR/prepare_environment.sh"
chmod +x "$SCRIPT_DIR/distribute_packages.sh"
chmod +x "$SCRIPT_DIR/verify_deployment.sh"

# 服务部署脚本
chmod +x "$SCRIPT_DIR/deploy_mongodb.sh"
chmod +x "$SCRIPT_DIR/deploy_redis.sh"
chmod +x "$SCRIPT_DIR/deploy_kafka.sh"
chmod +x "$SCRIPT_DIR/deploy_tdengine.sh"
chmod +x "$SCRIPT_DIR/deploy_nebula.sh"
chmod +x "$SCRIPT_DIR/deploy_dameng.sh"
chmod +x "$SCRIPT_DIR/deploy_monitoring.sh"

# 公共库
chmod +x "$SCRIPT_DIR/lib/common.sh"

echo "✓ 脚本权限设置完成"

# =============================================================================
# 创建必要目录
# =============================================================================

echo "创建必要目录..."

# 创建目录
mkdir -p "$SCRIPT_DIR/logs"
mkdir -p "$SCRIPT_DIR/templates"
mkdir -p "$SCRIPT_DIR/backup"

# 创建软件仓库目录（如果不存在）
if [[ ! -d "/apps/software-repo" ]]; then
    echo "创建软件仓库目录: /apps/software-repo"
    sudo mkdir -p /apps/software-repo
    sudo chmod 755 /apps/software-repo
fi

# 创建日志目录
if [[ ! -d "/var/log/deploy" ]]; then
    echo "创建部署日志目录: /var/log/deploy"
    sudo mkdir -p /var/log/deploy
    sudo chmod 755 /var/log/deploy
fi

echo "✓ 目录创建完成"

# =============================================================================
# 检查配置文件
# =============================================================================

echo "检查配置文件..."

# 检查全局配置
if [[ ! -f "$SCRIPT_DIR/config/global.conf" ]]; then
    echo "⚠ 全局配置文件不存在，请先配置: config/global.conf"
else
    echo "✓ 全局配置文件存在"
fi

# 检查主机配置
if [[ ! -f "$SCRIPT_DIR/config/hosts.conf" ]]; then
    echo "⚠ 主机配置文件不存在，请先配置: config/hosts.conf"
else
    echo "✓ 主机配置文件存在"
fi

# =============================================================================
# 检查系统依赖
# =============================================================================

echo "检查系统依赖..."

# 检查必需命令
required_commands=("ssh" "scp" "rsync" "wget" "curl" "tar" "gzip")
missing_commands=()

for cmd in "${required_commands[@]}"; do
    if ! command -v "$cmd" >/dev/null 2>&1; then
        missing_commands+=("$cmd")
    fi
done

if [[ ${#missing_commands[@]} -gt 0 ]]; then
    echo "⚠ 缺少必需命令: ${missing_commands[*]}"
    echo "请安装缺少的命令后重试"
else
    echo "✓ 系统依赖检查通过"
fi

# =============================================================================
# 检查SSH配置
# =============================================================================

echo "检查SSH配置..."

# 检查SSH密钥
if [[ -f "$HOME/.ssh/id_rsa" ]]; then
    echo "✓ SSH私钥存在"
else
    echo "⚠ SSH私钥不存在，建议生成SSH密钥对:"
    echo "  ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa"
fi

if [[ -f "$HOME/.ssh/id_rsa.pub" ]]; then
    echo "✓ SSH公钥存在"
else
    echo "⚠ SSH公钥不存在"
fi

# =============================================================================
# 生成示例配置
# =============================================================================

echo "生成示例配置文件..."

# 生成全局配置示例
if [[ ! -f "$SCRIPT_DIR/config/global.conf.example" ]]; then
    cat > "$SCRIPT_DIR/config/global.conf.example" << 'EOF'
#!/bin/bash
# 全局配置示例文件
# 复制此文件为 global.conf 并根据实际环境修改

# 基础配置
export DEPLOY_ENV="production"
export DEPLOY_USER="root"
export DEPLOY_KEY="/root/.ssh/id_rsa"
export SOFTWARE_REPO="/apps/software-repo"
export BACKUP_DIR="/backup"
export LOG_DIR="/var/log/deploy"

# 系统环境配置
export OS_VERSION="kylin_v10"
export CPU_ARCH="x86_64"
export TIMEZONE="Asia/Shanghai"

# 服务版本配置
export MONGODB_VERSION="4.4.18"
export REDIS_VERSION="7.0.8"
export KAFKA_VERSION="2.8.1"
export TDENGINE_VERSION="*******"
export NEBULA_VERSION="3.4.0"
export DAMENG_VERSION="*********"

# 编译配置（针对海光CPU）
export CC="gcc"
export CXX="g++"
export CFLAGS="-O2 -march=x86-64 -mtune=generic"
export CXXFLAGS="-O2 -march=x86-64 -mtune=generic"
export PORTABLE="1"
export USE_AVX="OFF"

# 安全配置
export ENABLE_FIREWALL="false"
export ENABLE_SELINUX="false"
EOF
    echo "✓ 生成全局配置示例: config/global.conf.example"
fi

# 生成主机配置示例
if [[ ! -f "$SCRIPT_DIR/config/hosts.conf.example" ]]; then
    cat > "$SCRIPT_DIR/config/hosts.conf.example" << 'EOF'
#!/bin/bash
# 主机配置示例文件
# 复制此文件为 hosts.conf 并根据实际环境修改

# MongoDB集群主机（副本集：1主2从）
declare -a MONGODB_HOSTS=(
    "*************"  # mongodb-01 (Primary)
    "*************"  # mongodb-02 (Secondary)
    "*************"  # mongodb-03 (Secondary)
)

# Redis集群主机（3主3从）
declare -a REDIS_HOSTS=(
    "*************"  # redis-01 (Master-1)
    "*************"  # redis-02 (Master-2)
    "*************"  # redis-03 (Master-3)
    "*************"  # redis-04 (Slave-1)
    "*************"  # redis-05 (Slave-2)
    "*************"  # redis-06 (Slave-3)
)

# Kafka集群主机（3节点）
declare -a KAFKA_HOSTS=(
    "*************"  # kafka-01
    "*************"  # kafka-02
    "*************"  # kafka-03
)

# TDEngine集群主机（3节点）
declare -a TDENGINE_HOSTS=(
    "*************"  # tdengine-01
    "*************"  # tdengine-02
    "*************"  # tdengine-03
)

# NebulaGraph集群主机（3节点）
declare -a NEBULA_HOSTS=(
    "*************"  # nebula-01
    "*************"  # nebula-02
    "*************"  # nebula-03
)

# 达梦数据库集群主机（1主2从）
declare -a DAMENG_HOSTS=(
    "*************"  # dm-01 (Primary)
    "*************"  # dm-02 (Standby)
    "*************"  # dm-03 (Standby)
)

# 监控服务主机（主备模式）
declare -a MONITOR_HOSTS=(
    "*************"  # monitor-01 (Prometheus + AlertManager)
    "*************"  # monitor-02 (Grafana + Backup)
)
EOF
    echo "✓ 生成主机配置示例: config/hosts.conf.example"
fi

# =============================================================================
# 输出使用指南
# =============================================================================

echo
echo "=== 初始化完成 ==="
echo
echo "下一步操作："
echo "1. 配置部署参数:"
echo "   cp config/global.conf.example config/global.conf"
echo "   cp config/hosts.conf.example config/hosts.conf"
echo "   vim config/global.conf"
echo "   vim config/hosts.conf"
echo
echo "2. 准备软件包:"
echo "   将所需软件包放入 /apps/software-repo 目录"
echo
echo "3. 配置SSH密钥:"
echo "   ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa"
echo "   ssh-copy-id root@target_host"
echo
echo "4. 执行部署:"
echo "   ./deploy_all.sh"
echo
echo "5. 验证部署:"
echo "   ./verify_deployment.sh"
echo
echo "详细文档请参考: README.md"
echo "==========================="

exit 0
