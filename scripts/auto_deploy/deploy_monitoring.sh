#!/bin/bash
# 监控系统部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化Prometheus+Grafana+AlertManager部署

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="监控系统部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 监控系统特定配置
# =============================================================================

# Prometheus配置
PROMETHEUS_USER="prometheus"
PROMETHEUS_GROUP="prometheus"
PROMETHEUS_HOME="/apps/prometheus"
PROMETHEUS_DATA_DIR="/apps/data/prometheus"
PROMETHEUS_CONFIG_DIR="$PROMETHEUS_HOME/config"

# Grafana配置
GRAFANA_USER="grafana"
GRAFANA_GROUP="grafana"
GRAFANA_HOME="/apps/grafana"
GRAFANA_DATA_DIR="/apps/data/grafana"

# AlertManager配置
ALERTMANAGER_USER="alertmanager"
ALERTMANAGER_GROUP="alertmanager"
ALERTMANAGER_HOME="/apps/alertmanager"
ALERTMANAGER_DATA_DIR="/apps/data/alertmanager"

# 安装配置
MONITORING_SOURCE_DIR="/apps/software/monitoring"

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_INSTALL=false
SKIP_CONFIG=false
SKIP_INIT=false
FORCE_REINSTALL=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-install      跳过安装"
            echo "  --skip-config       跳过配置生成"
            echo "  --skip-init         跳过服务初始化"
            echo "  --force-reinstall   强制重新安装"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# Prometheus安装函数
# =============================================================================

prepare_prometheus_environment() {
    local host=$1
    
    log_info "在 $host 上准备Prometheus环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备Prometheus环境"
        return 0
    fi
    
    # 创建Prometheus用户
    remote_execute "$host" "
        if ! id $PROMETHEUS_USER >/dev/null 2>&1; then
            groupadd -g 1007 $PROMETHEUS_GROUP
            useradd -u 1007 -g $PROMETHEUS_GROUP -r -s /bin/false -d /var/lib/prometheus $PROMETHEUS_USER
        fi
    "
    
    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $PROMETHEUS_HOME/{bin,config,rules,consoles,console_libraries}
        mkdir -p $PROMETHEUS_DATA_DIR/{data,backup}
        mkdir -p /var/log/prometheus
        
        chown -R $PROMETHEUS_USER:$PROMETHEUS_GROUP $PROMETHEUS_HOME $PROMETHEUS_DATA_DIR
        chown -R $PROMETHEUS_USER:$PROMETHEUS_GROUP /var/log/prometheus
        chmod 755 $PROMETHEUS_HOME $PROMETHEUS_DATA_DIR
    "
    
    log_info "Prometheus环境准备完成: $host"
}

install_prometheus() {
    local host=$1
    
    log_info "在 $host 上安装Prometheus..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上安装Prometheus"
        return 0
    fi
    
    # 检查是否已安装
    if remote_execute "$host" "test -f $PROMETHEUS_HOME/bin/prometheus" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "Prometheus已安装在 $host，跳过安装"
            return 0
        fi
    fi
    
    # 安装Prometheus
    remote_execute "$host" "
        cd $MONITORING_SOURCE_DIR
        
        # 查找Prometheus安装包
        if [[ -f prometheus-*.tar.gz ]]; then
            tar -zxf prometheus-*.tar.gz
            cp prometheus-*/prometheus $PROMETHEUS_HOME/bin/
            cp prometheus-*/promtool $PROMETHEUS_HOME/bin/
            cp -r prometheus-*/consoles/* $PROMETHEUS_HOME/consoles/
            cp -r prometheus-*/console_libraries/* $PROMETHEUS_HOME/console_libraries/
        else
            echo '错误: 找不到Prometheus安装包'
            exit 1
        fi
        
        # 设置权限
        chown -R $PROMETHEUS_USER:$PROMETHEUS_GROUP $PROMETHEUS_HOME
        chmod +x $PROMETHEUS_HOME/bin/*
        
        # 验证安装
        $PROMETHEUS_HOME/bin/prometheus --version
    "
    
    log_info "Prometheus安装完成: $host"
}

# =============================================================================
# Grafana安装函数
# =============================================================================

prepare_grafana_environment() {
    local host=$1
    
    log_info "在 $host 上准备Grafana环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备Grafana环境"
        return 0
    fi
    
    # 创建Grafana用户
    remote_execute "$host" "
        if ! id $GRAFANA_USER >/dev/null 2>&1; then
            groupadd -g 1008 $GRAFANA_GROUP
            useradd -u 1008 -g $GRAFANA_GROUP -r -s /bin/false -d /var/lib/grafana $GRAFANA_USER
        fi
    "
    
    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $GRAFANA_HOME/{bin,conf,data,logs,plugins,public}
        mkdir -p $GRAFANA_DATA_DIR/{data,logs,plugins,backup}
        
        chown -R $GRAFANA_USER:$GRAFANA_GROUP $GRAFANA_HOME $GRAFANA_DATA_DIR
        chmod 755 $GRAFANA_HOME $GRAFANA_DATA_DIR
    "
    
    log_info "Grafana环境准备完成: $host"
}

install_grafana() {
    local host=$1
    
    log_info "在 $host 上安装Grafana..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上安装Grafana"
        return 0
    fi
    
    # 检查是否已安装
    if remote_execute "$host" "test -f $GRAFANA_HOME/bin/grafana-server" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "Grafana已安装在 $host，跳过安装"
            return 0
        fi
    fi
    
    # 安装Grafana
    remote_execute "$host" "
        cd $MONITORING_SOURCE_DIR
        
        # 查找Grafana安装包
        if [[ -f grafana-*.tar.gz ]]; then
            tar -zxf grafana-*.tar.gz
            cp -r grafana-*/* $GRAFANA_HOME/
        elif [[ -f grafana-*.rpm ]]; then
            rpm -ivh grafana-*.rpm
        else
            echo '错误: 找不到Grafana安装包'
            exit 1
        fi
        
        # 设置权限
        chown -R $GRAFANA_USER:$GRAFANA_GROUP $GRAFANA_HOME
        chmod +x $GRAFANA_HOME/bin/*
        
        # 验证安装
        $GRAFANA_HOME/bin/grafana-server --version
    "
    
    log_info "Grafana安装完成: $host"
}

# =============================================================================
# AlertManager安装函数
# =============================================================================

prepare_alertmanager_environment() {
    local host=$1
    
    log_info "在 $host 上准备AlertManager环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备AlertManager环境"
        return 0
    fi
    
    # 创建AlertManager用户
    remote_execute "$host" "
        if ! id $ALERTMANAGER_USER >/dev/null 2>&1; then
            groupadd -g 1009 $ALERTMANAGER_GROUP
            useradd -u 1009 -g $ALERTMANAGER_GROUP -r -s /bin/false -d /var/lib/alertmanager $ALERTMANAGER_USER
        fi
    "
    
    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $ALERTMANAGER_HOME/{bin,config}
        mkdir -p $ALERTMANAGER_DATA_DIR/{data,backup}
        mkdir -p /var/log/alertmanager
        
        chown -R $ALERTMANAGER_USER:$ALERTMANAGER_GROUP $ALERTMANAGER_HOME $ALERTMANAGER_DATA_DIR
        chown -R $ALERTMANAGER_USER:$ALERTMANAGER_GROUP /var/log/alertmanager
        chmod 755 $ALERTMANAGER_HOME $ALERTMANAGER_DATA_DIR
    "
    
    log_info "AlertManager环境准备完成: $host"
}

install_alertmanager() {
    local host=$1
    
    log_info "在 $host 上安装AlertManager..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上安装AlertManager"
        return 0
    fi
    
    # 检查是否已安装
    if remote_execute "$host" "test -f $ALERTMANAGER_HOME/bin/alertmanager" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "AlertManager已安装在 $host，跳过安装"
            return 0
        fi
    fi
    
    # 安装AlertManager
    remote_execute "$host" "
        cd $MONITORING_SOURCE_DIR
        
        # 查找AlertManager安装包
        if [[ -f alertmanager-*.tar.gz ]]; then
            tar -zxf alertmanager-*.tar.gz
            cp alertmanager-*/alertmanager $ALERTMANAGER_HOME/bin/
            cp alertmanager-*/amtool $ALERTMANAGER_HOME/bin/
        else
            echo '错误: 找不到AlertManager安装包'
            exit 1
        fi
        
        # 设置权限
        chown -R $ALERTMANAGER_USER:$ALERTMANAGER_GROUP $ALERTMANAGER_HOME
        chmod +x $ALERTMANAGER_HOME/bin/*
        
        # 验证安装
        $ALERTMANAGER_HOME/bin/alertmanager --version
    "
    
    log_info "AlertManager安装完成: $host"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始监控系统部署..."
    
    # 获取锁
    if ! acquire_lock "deploy_monitoring"; then
        log_error "无法获取锁，可能有其他监控系统部署实例正在运行"
        exit 1
    fi
    
    # 检查监控主机配置
    if [[ ${#MONITOR_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置监控主机"
        exit 1
    fi
    
    log_info "监控系统主机: ${MONITOR_HOSTS[*]}"

    # 安装阶段
    if [[ "$SKIP_INSTALL" != "true" ]]; then
        # 在第一台主机安装Prometheus和AlertManager
        local prometheus_host="${MONITOR_HOSTS[0]}"
        prepare_prometheus_environment "$prometheus_host"
        install_prometheus "$prometheus_host"
        prepare_alertmanager_environment "$prometheus_host"
        install_alertmanager "$prometheus_host"

        # 在第二台主机安装Grafana（如果有）
        if [[ ${#MONITOR_HOSTS[@]} -gt 1 ]]; then
            local grafana_host="${MONITOR_HOSTS[1]}"
            prepare_grafana_environment "$grafana_host"
            install_grafana "$grafana_host"
        else
            # 如果只有一台主机，在同一台主机安装Grafana
            prepare_grafana_environment "$prometheus_host"
            install_grafana "$prometheus_host"
        fi
    else
        log_warn "跳过监控系统安装"
    fi

    # 配置阶段
    if [[ "$SKIP_CONFIG" != "true" ]]; then
        configure_monitoring_system
    else
        log_warn "跳过监控系统配置"
    fi

    # 启动和初始化阶段
    if [[ "$SKIP_INIT" != "true" ]]; then
        start_monitoring_services
        verify_monitoring_system
    else
        log_warn "跳过监控系统初始化"
    fi

    log_info "监控系统部署完成"
}

# =============================================================================
# 配置函数
# =============================================================================

configure_monitoring_system() {
    log_info "配置监控系统..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将配置监控系统"
        return 0
    fi

    local prometheus_host="${MONITOR_HOSTS[0]}"
    local grafana_host="${MONITOR_HOSTS[1]:-$prometheus_host}"

    # 配置Prometheus
    configure_prometheus "$prometheus_host"

    # 配置AlertManager
    configure_alertmanager "$prometheus_host"

    # 配置Grafana
    configure_grafana "$grafana_host"

    # 创建systemd服务
    create_monitoring_services "$prometheus_host" "$grafana_host"

    log_info "监控系统配置完成"
}

configure_prometheus() {
    local host=$1

    log_info "配置Prometheus: $host"

    # 生成Prometheus配置文件
    local prometheus_config="global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - \"$PROMETHEUS_CONFIG_DIR/rules/*.yml\"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - \"$host:$ALERTMANAGER_PORT\"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['$host:$PROMETHEUS_PORT']

  - job_name: 'node-exporter'
    static_configs:
      - targets:"

    # 添加所有主机的node-exporter
    local all_hosts=()
    all_hosts+=("${MONGODB_HOSTS[@]}")
    all_hosts+=("${REDIS_HOSTS[@]}")
    all_hosts+=("${KAFKA_HOSTS[@]}")
    all_hosts+=("${TDENGINE_HOSTS[@]}")
    all_hosts+=("${NEBULA_HOSTS[@]}")
    all_hosts+=("${DAMENG_HOSTS[@]}")
    all_hosts+=("${MONITOR_HOSTS[@]}")

    # 去重
    local unique_hosts=($(printf "%s\n" "${all_hosts[@]}" | sort -u))

    for target_host in "${unique_hosts[@]}"; do
        prometheus_config+="
        - '$target_host:9100'"
    done

    prometheus_config+="

  - job_name: 'mongodb'
    static_configs:
      - targets:"

    for mongodb_host in "${MONGODB_HOSTS[@]}"; do
        prometheus_config+="
        - '$mongodb_host:9216'"
    done

    prometheus_config+="

  - job_name: 'redis'
    static_configs:
      - targets:"

    for redis_host in "${REDIS_HOSTS[@]}"; do
        prometheus_config+="
        - '$redis_host:9121'"
    done

    # 写入配置文件
    remote_execute "$host" "cat > $PROMETHEUS_CONFIG_DIR/prometheus.yml << 'EOF'
$prometheus_config
EOF"

    # 创建告警规则目录
    remote_execute "$host" "mkdir -p $PROMETHEUS_CONFIG_DIR/rules"

    log_info "Prometheus配置完成: $host"
}

configure_alertmanager() {
    local host=$1

    log_info "配置AlertManager: $host"

    # 生成AlertManager配置文件
    local alertmanager_config="global:
  smtp_smarthost: '$SMTP_SERVER:$SMTP_PORT'
  smtp_from: '$ALERT_EMAIL'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '$NOTIFICATION_EMAIL'
    subject: '【工业场景监控告警】{{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      告警名称: {{ .Annotations.summary }}
      告警详情: {{ .Annotations.description }}
      告警时间: {{ .StartsAt }}
      告警主机: {{ .Labels.instance }}
      {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']"

    # 写入配置文件
    remote_execute "$host" "cat > $ALERTMANAGER_HOME/config/alertmanager.yml << 'EOF'
$alertmanager_config
EOF"

    log_info "AlertManager配置完成: $host"
}

configure_grafana() {
    local host=$1

    log_info "配置Grafana: $host"

    # 生成Grafana配置文件
    local grafana_config="[server]
http_addr = 0.0.0.0
http_port = $GRAFANA_PORT
domain = $host
root_url = http://$host:$GRAFANA_PORT/

[database]
type = sqlite3
path = $GRAFANA_DATA_DIR/data/grafana.db

[security]
admin_user = $GRAFANA_ADMIN_USER
admin_password = $(generate_password)
secret_key = $(generate_password)

[users]
allow_sign_up = false
default_theme = dark

[auth.anonymous]
enabled = false

[log]
mode = file
level = info
root_path = $GRAFANA_DATA_DIR/logs

[paths]
data = $GRAFANA_DATA_DIR/data
logs = $GRAFANA_DATA_DIR/logs
plugins = $GRAFANA_DATA_DIR/plugins"

    # 写入配置文件
    remote_execute "$host" "cat > $GRAFANA_HOME/conf/custom.ini << 'EOF'
$grafana_config
EOF"

    log_info "Grafana配置完成: $host"
}

create_monitoring_services() {
    local prometheus_host=$1
    local grafana_host=$2

    log_info "创建监控系统服务..."

    # 创建Prometheus服务
    local prometheus_service="[Unit]
Description=Prometheus Server
After=network.target

[Service]
User=$PROMETHEUS_USER
Group=$PROMETHEUS_GROUP
Type=simple
ExecStart=$PROMETHEUS_HOME/bin/prometheus \\
  --config.file=$PROMETHEUS_CONFIG_DIR/prometheus.yml \\
  --storage.tsdb.path=$PROMETHEUS_DATA_DIR/data \\
  --web.console.templates=$PROMETHEUS_HOME/consoles \\
  --web.console.libraries=$PROMETHEUS_HOME/console_libraries \\
  --web.listen-address=0.0.0.0:$PROMETHEUS_PORT \\
  --web.enable-lifecycle \\
  --storage.tsdb.retention.time=$PROMETHEUS_RETENTION
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target"

    remote_execute "$prometheus_host" "
        cat > /etc/systemd/system/prometheus.service << 'EOF'
$prometheus_service
EOF
        systemctl daemon-reload
        systemctl enable prometheus
    "

    # 创建AlertManager服务
    local alertmanager_service="[Unit]
Description=AlertManager
After=network.target

[Service]
User=$ALERTMANAGER_USER
Group=$ALERTMANAGER_GROUP
Type=simple
ExecStart=$ALERTMANAGER_HOME/bin/alertmanager \\
  --config.file=$ALERTMANAGER_HOME/config/alertmanager.yml \\
  --storage.path=$ALERTMANAGER_DATA_DIR/data \\
  --web.listen-address=0.0.0.0:$ALERTMANAGER_PORT
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target"

    remote_execute "$prometheus_host" "
        cat > /etc/systemd/system/alertmanager.service << 'EOF'
$alertmanager_service
EOF
        systemctl daemon-reload
        systemctl enable alertmanager
    "

    # 创建Grafana服务
    local grafana_service="[Unit]
Description=Grafana
After=network.target

[Service]
User=$GRAFANA_USER
Group=$GRAFANA_GROUP
Type=simple
ExecStart=$GRAFANA_HOME/bin/grafana-server \\
  --config=$GRAFANA_HOME/conf/custom.ini \\
  --homepath=$GRAFANA_HOME
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target"

    remote_execute "$grafana_host" "
        cat > /etc/systemd/system/grafana.service << 'EOF'
$grafana_service
EOF
        systemctl daemon-reload
        systemctl enable grafana
    "

    log_info "监控系统服务创建完成"
}

start_monitoring_services() {
    log_info "启动监控系统服务..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动监控系统服务"
        return 0
    fi

    local prometheus_host="${MONITOR_HOSTS[0]}"
    local grafana_host="${MONITOR_HOSTS[1]:-$prometheus_host}"

    # 启动Prometheus
    start_service "$prometheus_host" "prometheus"
    wait_for_port "$prometheus_host" "$PROMETHEUS_PORT" 60

    # 启动AlertManager
    start_service "$prometheus_host" "alertmanager"
    wait_for_port "$prometheus_host" "$ALERTMANAGER_PORT" 60

    # 启动Grafana
    start_service "$grafana_host" "grafana"
    wait_for_port "$grafana_host" "$GRAFANA_PORT" 60

    log_info "监控系统服务启动完成"
}

verify_monitoring_system() {
    log_info "验证监控系统..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将验证监控系统"
        return 0
    fi

    local prometheus_host="${MONITOR_HOSTS[0]}"
    local grafana_host="${MONITOR_HOSTS[1]:-$prometheus_host}"

    # 验证Prometheus
    if remote_execute "$prometheus_host" "curl -s http://$prometheus_host:$PROMETHEUS_PORT/-/healthy" | grep -q "Prometheus is Healthy"; then
        log_info "Prometheus验证成功"
    else
        log_error "Prometheus验证失败"
        return 1
    fi

    # 验证AlertManager
    if remote_execute "$prometheus_host" "curl -s http://$prometheus_host:$ALERTMANAGER_PORT/-/healthy" | grep -q "OK"; then
        log_info "AlertManager验证成功"
    else
        log_error "AlertManager验证失败"
        return 1
    fi

    # 验证Grafana
    if remote_execute "$grafana_host" "curl -s http://$grafana_host:$GRAFANA_PORT/api/health" | grep -q "ok"; then
        log_info "Grafana验证成功"
    else
        log_error "Grafana验证失败"
        return 1
    fi

    log_info "监控系统验证完成"
}

# 执行主函数
main "$@"
