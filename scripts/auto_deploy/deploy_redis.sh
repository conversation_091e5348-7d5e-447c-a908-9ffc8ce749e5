#!/bin/bash
# Redis集群部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化Redis集群部署

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="Redis集群部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# Redis特定配置
# =============================================================================

# Redis配置
REDIS_USER="redis"
REDIS_GROUP="redis"
REDIS_HOME="/apps/redis"
REDIS_DATA_DIR="/apps/data/redis"
REDIS_LOG_DIR="/var/log/redis"
REDIS_CONFIG_DIR="$REDIS_HOME/conf"

# 编译配置
REDIS_SOURCE_DIR="/apps/software/redis"
REDIS_BUILD_DIR="/tmp/redis-build"
unset REDIS_PORTS
# Redis集群端口配置
IFS=',' read -ra REDIS_PORTS <<< "$REDIS_CLUSTER_PORTS"

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_COMPILE=false
SKIP_CONFIG=false
SKIP_INIT=false
FORCE_REINSTALL=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-compile)
            SKIP_COMPILE=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-compile      跳过编译安装"
            echo "  --skip-config       跳过配置生成"
            echo "  --skip-init         跳过集群初始化"
            echo "  --force-reinstall   强制重新安装"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# Redis编译安装函数
# =============================================================================

prepare_redis_environment() {
    local host=$1
    
    log_info "在 $host 上准备Redis环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备Redis环境"
        return 0
    fi
    
    # 创建Redis用户
    remote_execute "$host" "
        if ! id $REDIS_USER >/dev/null 2>&1; then
            groupadd -g 1002 $REDIS_GROUP
            useradd -u 1002 -g $REDIS_GROUP -r -s /bin/false -d /var/lib/redis $REDIS_USER
        fi
    "
    
    # 检查离线环境
    check_offline_environment "$host"

    # 配置离线YUM仓库（如果是离线模式）
    setup_offline_yum_repo "$host"
    log_info "在 $host 创建redie部署目录..."
    ports_str=$(IFS=','; echo "${REDIS_PORTS[*]}")

    log_info "${REDIS_PORTS[@]}"

    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $REDIS_HOME/{bin,conf,logs}
        mkdir -p $REDIS_DATA_DIR/{data,log,backup}
        mkdir -p /var/run/redis
        # 为每个端口创建数据目录
        IFS=',' read -r -a REDIS_PORTS <<< '$ports_str'
        for port in \"\${REDIS_PORTS[@]}\"; do
            mkdir -p $REDIS_DATA_DIR/\$port
        done
        chown -R $REDIS_USER:$REDIS_GROUP $REDIS_HOME $REDIS_DATA_DIR /var/run/redis
        chmod 755 $REDIS_HOME $REDIS_DATA_DIR
        chmod 750 /var/run/redis
    "

    # 安装编译依赖
    install_system_packages "$host" \
        "gcc" "gcc-c++" "make" "cmake" "autoconf" \
        "automake" "libtool" "pkgconfig" "patch" "openssl-devel" \
        "zlib-devel" "libcurl-devel" "readline-devel" "ncurses-devel" "libaio-devel" \
        "numactl-devel" "jemalloc-devel" "python3" "python3-pip" "python3-devel" \
        "binutils" "bison" "flex" "gdb" "gettext" \
        "glibc-devel" "openEuler-rpm-config" "pkgconf" "rpm" "rpm-build" \
        "asciidoc" "byacc" "ctags" "diffstat" "elfutils" \
        "gcc-gfortran" "git" "intltool" "ltrace" "patchutils" \
        "perl-Fedora-VSP" "perl-generators" "pesign" "source-highlight" "subversion" \
        "systemtap" "valgrind" "valgrind-devel" "babel" "chrpath" \
        "expect" "gcc-objc" "gcc-objc++" "mercurial" "mod_dav_svn" \
        "rpmdevtools" "rpmlint" "systemtap-sdt-devel" "systemtap-server" \
    
    log_info "Redis环境准备完成: $host"
}

compile_redis() {
    local host=$1
    
    log_info "在 $host 上编译Redis..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上编译Redis"
        return 0
    fi
    
    # 检查是否已安装
    if remote_execute "$host" "test -f $REDIS_HOME/bin/redis-server" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "Redis已安装在 $host，跳过编译"
            return 0
        else
            log_info "强制重新编译Redis在 $host"
        fi
    fi
    
    # 解压和编译Redis
    remote_execute "$host" "
        cd $REDIS_SOURCE_DIR

        # 离线模式优先查找预编译包
        if [[ '$OFFLINE_MODE' == 'true' ]]; then
            if [[ -f redis-hygon-precompiled.tar.gz ]]; then
                echo '使用离线预编译Redis包'
                tar -zxf redis-hygon-precompiled.tar.gz
                cp -r redis-hygon-precompiled/* $REDIS_HOME/
                chown -R $REDIS_USER:$REDIS_GROUP $REDIS_HOME
                echo 'Redis离线安装完成'
                exit 0
            fi
        fi

        # 源码编译模式
        # 查找Redis源码包
        if [[ -f redis-without-avx.tar.gz ]]; then
            tar -zxf redis-without-avx.tar.gz
            cd redis-without-avx
        elif [[ -f redis-7.0.8.tar.gz ]]; then
            tar -zxf redis-7.0.8.tar.gz
            cd redis-7.0.8
        else
            echo '错误: 找不到Redis源码包或预编译包'
            echo '离线模式请准备: redis-hygon-precompiled.tar.gz'
            echo '源码模式请准备: redis-7.0.8.tar.gz'
            exit 1
        fi
        
        # 设置编译环境（针对海光CPU优化）
        export CC=gcc
        export CFLAGS='-O2 -march=x86-64 -mtune=generic'
        export PORTABLE=1
        export USE_AVX=OFF
        
        # 编译Redis
        make clean
        make -j\$(nproc) PREFIX=$REDIS_HOME install
        
        # 复制配置文件模板
        cp redis.conf $REDIS_CONFIG_DIR/redis-template.conf
        cp sentinel.conf $REDIS_CONFIG_DIR/sentinel-template.conf
        
        # 设置环境变量
        echo 'export REDIS_HOME=$REDIS_HOME' >> /etc/profile
        echo 'export PATH=\$PATH:\$REDIS_HOME/bin' >> /etc/profile
    "
    
    # 验证安装
    if remote_execute "$host" "$REDIS_HOME/bin/redis-server --version"; then
        log_info "Redis编译安装成功: $host"
    else
        log_error "Redis编译安装失败: $host"
        return 1
    fi
}

# =============================================================================
# Redis配置函数
# =============================================================================
generate_redis_config() {
    local host=$1
    local port=$2
    local password=$3
    local master_password=$4
    local is_master=${5:-true}
    
    log_info "为 $host:$port 生成Redis配置文件..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host:$port 生成Redis配置文件"
        return 0
    fi
    
    # 生成Redis配置文件
    local config_content="# Redis配置文件 - $host:$port
# 基础配置
port $port
bind 0.0.0.0
protected-mode no
daemonize yes
pidfile /var/run/redis/redis-$port.pid
logfile $REDIS_DATA_DIR/log/redis-$port.log
loglevel notice

# 数据目录
dir $REDIS_DATA_DIR/$port
dbfilename dump-$port.rdb

# 内存配置
maxmemory $REDIS_MAX_MEMORY
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes

# AOF配置
appendonly yes
appendfilename \"appendonly-$port.aof\"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 集群配置
cluster-enabled yes
cluster-config-file nodes-$port.conf
cluster-node-timeout 15000
cluster-require-full-coverage no

# 安全配置
requirepass $password
masterauth $master_password

# 网络优化
tcp-keepalive 300
timeout 0
tcp-backlog 511

# 客户端配置
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 内存优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
"
    
    # 写入配置文件
    remote_execute "$host" "cat > $REDIS_CONFIG_DIR/redis-$port.conf << 'EOF'
$config_content
EOF"
    
    log_info "Redis配置文件生成完成: $host:$port"
}

create_redis_service() {
    local host=$1
    local port=$2
    
    log_info "在 $host 上创建Redis-$port systemd服务..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上创建Redis-$port服务"
        return 0
    fi
    
    local service_content="[Unit]
Description=Redis In-Memory Data Store (Port $port)
After=network.target

[Service]
User=$REDIS_USER
Group=$REDIS_GROUP
Type=forking
PIDFile=/var/run/redis/redis-$port.pid
ExecStart=$REDIS_HOME/bin/redis-server $REDIS_CONFIG_DIR/redis-$port.conf
ExecReload=/bin/kill -HUP \$MAINPID
ExecStop=$REDIS_HOME/bin/redis-cli -p $port shutdown
Restart=always
RestartSec=10
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target"
    
    remote_execute "$host" "
        cat > /etc/systemd/system/redis-$port.service << 'EOF'
$service_content
EOF
        systemctl daemon-reload
        systemctl enable redis-$port
    "
    
    log_info "Redis-$port服务创建完成: $host"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始Redis集群部署..."
    
    # 获取锁
    if ! acquire_lock "deploy_redis"; then
        log_error "无法获取锁，可能有其他Redis部署实例正在运行"
        exit 1
    fi
    
    # 检查Redis主机配置
    if [[ ${#REDIS_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置Redis主机"
        exit 1
    fi
    local password=$(generate_password)
    local master_password=$(generate_password)
    log_info "Redis集群主机: ${REDIS_HOSTS[*]}"
    log_info "Redis集群端口: ${REDIS_PORTS[*]}"

    for host in "${REDIS_HOSTS[@]}"; do
      if remote_execute "$host" "test -f $REDIS_HOME/bin/redis-server" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "Redis已安装在 $host，跳过编译"
        else
            # 编译安装阶段
            if [[ "$SKIP_COMPILE" != "true" ]]; then
                prepare_redis_environment "$host"
                compile_redis "$host"
            else
                log_warn "跳过Redis编译安装"
            fi

            # 配置阶段
            if [[ "$SKIP_CONFIG" != "true" ]]; then
                local host_index=0
                # 每个主机配置一个端口
                local port=${REDIS_PORTS[$host_index]}
                generate_redis_config "$host" "$port" "$password" "$master_password"
                create_redis_service "$host" "$port"
                host_index=$((host_index + 1))
            else
                log_warn "跳过Redis配置"
            fi
        fi
      else
        if [[ "$SKIP_COMPILE" != "true" ]]; then
          prepare_redis_environment "$host"
          compile_redis "$host"
        else
            log_warn "跳过Redis编译安装"
        fi

        # 配置阶段
        if [[ "$SKIP_CONFIG" != "true" ]]; then
            local host_index=0
            # 每个主机配置一个端口
            local port=${REDIS_PORTS[$host_index]}
            generate_redis_config "$host" "$port" "$password" "$master_password"
            create_redis_service "$host" "$port"
            host_index=$((host_index + 1))
        else
            log_warn "跳过Redis配置"
        fi
      fi
    done


    local all_installed=true
    local host_index=0
    for host in "${REDIS_HOSTS[@]}"; do
        # 每个主机配置一个端口
        local port=${REDIS_PORTS[$host_index]}
        if ! check_redis_installed "$host" "$port"; then
            all_installed=false
            log_info "$host 节点未检测到完整redis安装"
        fi
        host_index=$((host_index + 1))
    done

    if [[ "$all_installed" == "true" ]]; then
        release_lock "deploy_redis"
        exit 0
    fi
    # 启动服务
    if [[ "$SKIP_INIT" != "true" ]]; then
        start_redis_cluster
        initialize_redis_cluster
    else
        log_warn "跳过Redis集群初始化"
    fi

    log_info "Redis集群部署完成"
}

check_redis_installed() {
    local host=$1
    local port=$2
    local service_running=false

    # 检查服务是否运行
    if remote_execute "$host" "systemctl is-active --quiet redis-$port"; then
        log_info "redis服务已在运行: $host:$port"
        service_running=true
    fi

    # 如果服务已完整安装
    if [[ "$service_running" == "true" ]]; then
        return 0  # 已安装
    fi

    return 1  # 未安装
}

# =============================================================================
# Redis集群初始化函数
# =============================================================================

start_redis_cluster() {
    log_info "启动Redis集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动Redis集群"
        return 0
    fi

    # 启动所有Redis实例
    local host_index=0
    for host in "${REDIS_HOSTS[@]}"; do
        local port=${REDIS_PORTS[$host_index]}
        log_info "启动Redis服务: $host:$port"
        start_service "$host" "redis-$port"

        # 等待服务启动
        if ! wait_for_port "$host" "$port" 60; then
            log_error "Redis服务启动失败: $host:$port"
            return 1
        fi
        host_index=$((host_index + 1))
    done

    log_info "所有Redis节点启动完成"
}

initialize_redis_cluster() {
    log_info "初始化Redis集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将初始化Redis集群"
        return 0
    fi

    # 构建集群节点列表
    local cluster_nodes=()
    local host_index=0
    for host in "${REDIS_HOSTS[@]}"; do
        local port=${REDIS_PORTS[$host_index]}
        cluster_nodes+=("$host:$port")
        host_index=$((host_index + 1))
    done

    # 使用第一个节点执行集群初始化
    local first_host="${REDIS_HOSTS[0]}"
    local first_port="${REDIS_PORTS[0]}"

    log_info "在 $first_host:$first_port 上执行集群初始化..."
    local config_file="$REDIS_CONFIG_DIR/redis-$first_port.conf"
    local password_command="grep '^requirepass' \"$config_file\" | awk '{print \$2}'"
    local cluster_password
    cluster_password=$(ssh -o ConnectTimeout=$DEPLOY_TIMEOUT -o BatchMode=yes "$DEPLOY_USER@$first_host" "$password_command" 2>&1)
    log_info "Redis集群密码: $cluster_password"
    # 检查密码是否有效
    if [[ -z "$cluster_password" ]]; then
        log_error "无法从配置文件 $config_file 中获取密码"
        return 1
    fi
    local cluster_command="echo 'yes' | $REDIS_HOME/bin/redis-cli -h $first_host -p $first_port -a $cluster_password --cluster create ${cluster_nodes[*]} --cluster-replicas 1"
    # 创建集群
    remote_execute "$first_host" "$cluster_command"

    # 等待集群稳定
    log_info "等待集群稳定..."
    sleep 30

    # 验证集群状态
    if remote_execute "$first_host" "$REDIS_HOME/bin/redis-cli -p $first_port cluster info"; then
        log_info "Redis集群初始化成功"
    else
        log_error "Redis集群初始化失败"
        return 1
    fi

    # 显示集群节点信息
    remote_execute "$first_host" "$REDIS_HOME/bin/redis-cli -p $first_port cluster nodes"
}

# 执行主函数
main "$@"
