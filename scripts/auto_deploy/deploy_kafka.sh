#!/bin/bash
# Kafka集群部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化Kafka集群部署

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="Kafka集群部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# Kafka特定配置
# =============================================================================

# Kafka配置
KAFKA_USER="kafka"
KAFKA_GROUP="kafka"
KAFKA_HOME="/apps/kafka"
KAFKA_DATA_DIR="/apps/data/kafka"
KAFKA_LOG_DIR="/var/log/kafka"
KAFKA_CONFIG_DIR="$KAFKA_HOME/config"

# ZooKeeper配置
ZOOKEEPER_HOME="/apps/zookeeper"
ZOOKEEPER_DATA_DIR="/apps/data/zookeeper"
ZOOKEEPER_LOG_DIR="/var/log/zookeeper"

# 软件包配置
KAFKA_SOURCE_DIR="/apps/software/kafka"

KAFKA_JDK_SOURCE_DIR="/apps/software/jdk"
# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_INSTALL=false
SKIP_CONFIG=false
SKIP_INIT=false
FORCE_REINSTALL=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-install      跳过安装"
            echo "  --skip-config       跳过配置生成"
            echo "  --skip-init         跳过集群初始化"
            echo "  --force-reinstall   强制重新安装"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# JDK安装函数
# =============================================================================

install_jdk() {
    local host=$1
    
    log_info "在 $host 上安装JDK..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上安装JDK"
        return 0
    fi
    
    # 检查JDK是否已安装
    if remote_execute "$host" "java -version" >/dev/null 2>&1; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "JDK已安装在 $host，跳过安装"
            return 0
        fi
    fi
    
    # 检查离线环境
    check_offline_environment "$host"

    # 配置离线YUM仓库（如果是离线模式）
    setup_offline_yum_repo "$host"

    # 安装华为毕昇JDK（适配海光CPU）
    remote_execute "$host" "
        cd $KAFKA_JDK_SOURCE_DIR

        # 查找JDK安装包
        if [[ -f bisheng-jdk-11.0.16-linux-x64.tar.gz ]]; then
            echo '使用华为毕昇JDK（海光CPU优化）'
            tar -zxf bisheng-jdk-11.0.16-linux-x64.tar.gz -C /apps/
            ln -sf /apps/bisheng-jdk-* $JAVA_HOME
        elif [[ -f openjdk-*.tar.gz ]]; then
            echo '使用OpenJDK'
            tar -zxf openjdk-*.tar.gz -C /apps/
            ln -sf /apps/jdk-* $JAVA_HOME
        elif [[ '$OFFLINE_MODE' != 'true' ]]; then
            # 在线模式使用系统包管理器安装
            echo '在线安装OpenJDK'
            yum install -y java-11-openjdk java-11-openjdk-devel
            export JAVA_HOME=/usr/lib/jvm/java-11-openjdk
        else
            echo '错误: 离线模式下找不到JDK安装包'
            echo '请准备: bisheng-jdk-11.0.16-linux-x64.tar.gz 或 openjdk-*.tar.gz'
            exit 1
        fi
        
        # 设置环境变量
        echo 'export JAVA_HOME=$JAVA_HOME' >> /etc/profile
        echo 'export PATH=\$PATH:\$JAVA_HOME/bin' >> /etc/profile
        source /etc/profile
        
        # 验证安装
        java -version
        javac -version
    "
    
    log_info "JDK安装完成: $host"
}

# =============================================================================
# ZooKeeper安装配置函数
# =============================================================================

prepare_zookeeper_environment() {
    local host=$1
    
    log_info "在 $host 上准备ZooKeeper环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备ZooKeeper环境"
        return 0
    fi
    
    # 创建ZooKeeper用户
    remote_execute "$host" "
        if ! id $KAFKA_USER >/dev/null 2>&1; then
            groupadd -g 1003 $KAFKA_GROUP
            useradd -u 1003 -g $KAFKA_GROUP -r -s /bin/false -d /var/lib/kafka $KAFKA_USER
        fi
    "
    
    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $ZOOKEEPER_HOME/{bin,conf,logs}
        mkdir -p $ZOOKEEPER_DATA_DIR/{data,log,backup}
        mkdir -p $KAFKA_HOME/{bin,config,logs}
        mkdir -p $KAFKA_DATA_DIR/{data,log,backup}
        mkdir -p /var/run/kafka
        
        chown -R $KAFKA_USER:$KAFKA_GROUP $ZOOKEEPER_HOME $ZOOKEEPER_DATA_DIR
        chown -R $KAFKA_USER:$KAFKA_GROUP $KAFKA_HOME $KAFKA_DATA_DIR /var/run/kafka
        chmod 755 $ZOOKEEPER_HOME $ZOOKEEPER_DATA_DIR $KAFKA_HOME $KAFKA_DATA_DIR
        chmod 750 /var/run/kafka
    "
    
    log_info "ZooKeeper环境准备完成: $host"
}

install_kafka_zookeeper() {
    local host=$1
    
    log_info "在 $host 上安装Kafka和ZooKeeper..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上安装Kafka和ZooKeeper"
        return 0
    fi
    
    # 检查是否已安装
    if remote_execute "$host" "test -f $KAFKA_HOME/bin/kafka-server-start.sh" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "Kafka已安装在 $host，跳过安装"
            return 0
        else
            log_info "强制重新安装Kafka在 $host"
        fi
    fi
    
    # 解压安装Kafka
    remote_execute "$host" "
        cd $KAFKA_SOURCE_DIR

        # 查找Kafka安装包
        if [[ -f kafka_2.13-2.8.1.tgz ]]; then
            echo '使用Kafka二进制包'
            tar -zxf kafka_2.13-2.8.1.tgz
            cp -r kafka_2.13-2.8.1/* $KAFKA_HOME/
        elif [[ -f kafka-*.tar.gz ]]; then
            echo '使用Kafka压缩包'
            tar -zxf kafka-*.tar.gz
            cp -r kafka-*/* $KAFKA_HOME/
        elif [[ -f apache-kafka-*.tar.gz ]]; then
            echo '使用Apache Kafka官方包'
            tar -zxf apache-kafka-*.tar.gz
            cp -r apache-kafka-*/* $KAFKA_HOME/
        else
            echo '错误: 找不到Kafka安装包'
            echo '请准备以下格式之一的安装包:'
            echo '  - kafka_2.13-2.8.1.tgz'
            echo '  - kafka-*.tar.gz'
            echo '  - apache-kafka-*.tar.gz'
            exit 1
        fi
        
        # 设置权限
        chown -R $KAFKA_USER:$KAFKA_GROUP $KAFKA_HOME
        
        # 设置环境变量
        echo 'export KAFKA_HOME=$KAFKA_HOME' >> /etc/profile
        echo 'export PATH=\$PATH:\$KAFKA_HOME/bin' >> /etc/profile
        
        # 验证安装
        ls -la $KAFKA_HOME/bin/kafka-server-start.sh
    "
    
    log_info "Kafka安装完成: $host"
}

# =============================================================================
# 配置函数
# =============================================================================

generate_zookeeper_config() {
    local host=$1
    local server_id=$2
    
    log_info "为 $host 生成ZooKeeper配置文件 (ID: $server_id)..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host 生成ZooKeeper配置文件"
        return 0
    fi
    
    # 生成ZooKeeper配置
    local zoo_config="# ZooKeeper配置文件 - $host
tickTime=2000
initLimit=10
syncLimit=5
dataDir=$ZOOKEEPER_DATA_DIR/data
dataLogDir=$ZOOKEEPER_DATA_DIR/log
clientPort=$ZOOKEEPER_PORT
maxClientCnxns=60
autopurge.snapRetainCount=3
autopurge.purgeInterval=1

# 集群配置
"
    
    # 添加集群节点配置
    local i=1
    for kafka_host in "${KAFKA_HOSTS[@]}"; do
        zoo_config+="server.$i=$kafka_host:2888:3888
"
        i=$((i + 1))
    done
    
    # 写入配置文件
    remote_execute "$host" "
        cat > $KAFKA_HOME/config/zookeeper.properties << 'EOF'
$zoo_config
EOF
        
        # 创建myid文件
        echo '$server_id' > $ZOOKEEPER_DATA_DIR/data/myid
        chown $KAFKA_USER:$KAFKA_GROUP $ZOOKEEPER_DATA_DIR/data/myid
    "
    
    log_info "ZooKeeper配置文件生成完成: $host"
}

generate_kafka_config() {
    local host=$1
    local broker_id=$2
    
    log_info "为 $host 生成Kafka配置文件 (Broker ID: $broker_id)..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host 生成Kafka配置文件"
        return 0
    fi
    
    # 构建ZooKeeper连接字符串
    local zk_connect=""
    for kafka_host in "${KAFKA_HOSTS[@]}"; do
        if [[ -n "$zk_connect" ]]; then
            zk_connect+=","
        fi
        zk_connect+="$kafka_host:$ZOOKEEPER_PORT"
    done
    
    # 生成Kafka配置
    local kafka_config="# Kafka配置文件 - $host
broker.id=$broker_id
listeners=PLAINTEXT://$host:$KAFKA_PORT
advertised.listeners=PLAINTEXT://$host:$KAFKA_PORT
num.network.threads=8
num.io.threads=16
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600

# 日志配置
log.dirs=$KAFKA_DATA_DIR/data
num.partitions=3
num.recovery.threads.per.data.dir=2
offsets.topic.replication.factor=$KAFKA_REPLICATION_FACTOR
transaction.state.log.replication.factor=$KAFKA_REPLICATION_FACTOR
transaction.state.log.min.isr=2
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000

# ZooKeeper配置
zookeeper.connect=$zk_connect
zookeeper.connection.timeout.ms=18000

# 集群配置
group.initial.rebalance.delay.ms=0
auto.create.topics.enable=false
delete.topic.enable=true

# 性能优化
replica.fetch.max.bytes=1048576
message.max.bytes=1000000
replica.fetch.wait.max.ms=500
fetch.purgatory.purge.interval.requests=1000
producer.purgatory.purge.interval.requests=1000
"
    
    # 写入配置文件
    remote_execute "$host" "cat > $KAFKA_HOME/config/server.properties << 'EOF'
$kafka_config
EOF"
    
    log_info "Kafka配置文件生成完成: $host"
}


# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始Kafka集群部署..."
    
    # 获取锁
    if ! acquire_lock "deploy_kafka"; then
        log_error "无法获取锁，可能有其他Kafka部署实例正在运行"
        exit 1
    fi
    
    # 检查Kafka主机配置
    if [[ ${#KAFKA_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置Kafka主机"
        exit 1
    fi
    
    log_info "Kafka集群主机: ${KAFKA_HOSTS[*]}"



    # 安装阶段
    if [[ "$SKIP_INSTALL" != "true" ]]; then
        for host in "${KAFKA_HOSTS[@]}"; do
            if remote_execute "$host" "test -f $KAFKA_HOME/bin/kafka-server-start.sh" 2>/dev/null; then
              log_info "$host 节点已安装Kafka"
            else
              log_info "开始安装Kafka到 $host 节点..."
              install_jdk "$host"
              prepare_zookeeper_environment "$host"
              install_kafka_zookeeper "$host"
              # 配置阶段
              if [[ "$SKIP_CONFIG" != "true" ]]; then
                  local server_id=1
                  generate_zookeeper_config "$host" "$server_id"
                  generate_kafka_config "$host" "$server_id"
                  create_kafka_services "$host"
                  server_id=$((server_id + 1))
              else
                  log_warn "跳过Kafka配置"
              fi
            fi
        done
    else
        log_warn "跳过Kafka安装"
    fi

    local all_started=true
    for host in "${KAFKA_HOSTS[@]}"; do
        if ! check_kafka_started "$host"; then
            all_started=false
            log_info "$host 节点未检测到完整kafka安装"
        fi
    done

    if [[ "$all_started" == "true" ]]; then
        release_lock "deploy_kafka"
        exit 0
    fi

    # 启动和初始化阶段
    if [[ "$SKIP_INIT" != "true" ]]; then
        start_zookeeper_cluster
        start_kafka_cluster
        verify_kafka_cluster
    else
        log_warn "跳过Kafka集群初始化"
    fi

    log_info "Kafka集群部署完成"
}

check_kafka_started() {
    local host=$1
    local service_running=false

    # 检查服务是否运行
    if remote_execute "$host" "systemctl is-active --quiet kafka"; then
        log_info "kafka服务已在运行: $host"
        service_running=true
    fi

    # 如果服务已完整安装
    if [[ "$service_running" == "true" ]]; then
        return 0  # 已安装
    fi

    return 1  # 未安装
}

# =============================================================================
# 服务管理函数
# =============================================================================

create_kafka_services() {
    local host=$1

    log_info "在 $host 上创建Kafka服务..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上创建Kafka服务"
        return 0
    fi

    # 创建ZooKeeper服务
    local zk_service_content="[Unit]
Description=Apache ZooKeeper
After=network.target

[Service]
User=$KAFKA_USER
Group=$KAFKA_GROUP
Type=forking
Environment=JAVA_HOME=$JAVA_HOME
Environment=KAFKA_HOME=$KAFKA_HOME
ExecStart=$KAFKA_HOME/bin/zookeeper-server-start.sh -daemon $KAFKA_HOME/config/zookeeper.properties
ExecStop=$KAFKA_HOME/bin/zookeeper-server-stop.sh
Restart=always
RestartSec=10
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target"

    # 创建Kafka服务
    local kafka_service_content="[Unit]
Description=Apache Kafka
After=network.target zookeeper.service
Requires=zookeeper.service

[Service]
User=$KAFKA_USER
Group=$KAFKA_GROUP
Type=forking
Environment=JAVA_HOME=$JAVA_HOME
Environment=KAFKA_HOME=$KAFKA_HOME
Environment=KAFKA_HEAP_OPTS=\"-Xmx$JVM_HEAP_SIZE -Xms$JVM_HEAP_SIZE\"
Environment=KAFKA_JVM_PERFORMANCE_OPTS=\"-server -XX:+UseG1GC -XX:MaxGCPauseMillis=20 -XX:InitiatingHeapOccupancyPercent=35\"
ExecStart=$KAFKA_HOME/bin/kafka-server-start.sh -daemon $KAFKA_HOME/config/server.properties
ExecStop=$KAFKA_HOME/bin/kafka-server-stop.sh
Restart=always
RestartSec=10
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target"

    remote_execute "$host" "
        # 创建ZooKeeper服务
        cat > /etc/systemd/system/zookeeper.service << 'EOF'
$zk_service_content
EOF

        # 创建Kafka服务
        cat > /etc/systemd/system/kafka.service << 'EOF'
$kafka_service_content
EOF

        systemctl daemon-reload
        systemctl enable zookeeper
        systemctl enable kafka
    "

    log_info "Kafka服务创建完成: $host"
}

start_zookeeper_cluster() {
    log_info "启动ZooKeeper集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动ZooKeeper集群"
        return 0
    fi

    # 启动所有ZooKeeper节点
    for host in "${KAFKA_HOSTS[@]}"; do
        log_info "启动ZooKeeper服务: $host"
        start_service "$host" "zookeeper"

        # 等待服务启动
        if ! wait_for_port "$host" "$ZOOKEEPER_PORT" 60; then
            log_error "ZooKeeper服务启动失败: $host"
            return 1
        fi
    done

    # 等待集群稳定
    log_info "等待ZooKeeper集群稳定..."
    sleep 30

    log_info "ZooKeeper集群启动完成"
}

start_kafka_cluster() {
    log_info "启动Kafka集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动Kafka集群"
        return 0
    fi

    # 启动所有Kafka节点
    for host in "${KAFKA_HOSTS[@]}"; do
        log_info "启动Kafka服务: $host"
        start_service "$host" "kafka"

        # 等待服务启动
        if ! wait_for_port "$host" "$KAFKA_PORT" 60; then
            log_error "Kafka服务启动失败: $host"
            return 1
        fi
    done

    # 等待集群稳定
    log_info "等待Kafka集群稳定..."
    sleep 30

    log_info "Kafka集群启动完成"
}

verify_kafka_cluster() {
    log_info "验证Kafka集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将验证Kafka集群"
        return 0
    fi

    local first_host="${KAFKA_HOSTS[0]}"

    # 创建测试主题
    remote_execute "$first_host" "
        $KAFKA_HOME/bin/kafka-topics.sh --create \
            --bootstrap-server $first_host:$KAFKA_PORT \
            --topic test-topic \
            --partitions 3 \
            --replication-factor $KAFKA_REPLICATION_FACTOR
    "

    # 列出主题
    if remote_execute "$first_host" "$KAFKA_HOME/bin/kafka-topics.sh --list --bootstrap-server $first_host:$KAFKA_PORT"; then
        log_info "Kafka集群验证成功"
    else
        log_error "Kafka集群验证失败"
        return 1
    fi

    # 删除测试主题
    remote_execute "$first_host" "
        $KAFKA_HOME/bin/kafka-topics.sh --delete \
            --bootstrap-server $first_host:$KAFKA_PORT \
            --topic test-topic
    "
}

# 执行主函数
main "$@"
