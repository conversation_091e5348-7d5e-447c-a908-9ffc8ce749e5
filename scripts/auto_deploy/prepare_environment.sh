#!/bin/bash
# 环境准备脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化环境准备和系统优化

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="环境准备脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_SYSTEM_CHECK=false
SKIP_PACKAGE_INSTALL=true
SKIP_OPTIMIZATION=false
FORCE_REINSTALL=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-system-check)
            SKIP_SYSTEM_CHECK=true
            shift
            ;;
        --skip-package-install)
            SKIP_PACKAGE_INSTALL=true
            shift
            ;;
        --skip-optimization)
            SKIP_OPTIMIZATION=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-system-check     跳过系统检查"
            echo "  --skip-package-install  跳过软件包安装"
            echo "  --skip-optimization     跳过系统优化"
            echo "  --force-reinstall       强制重新安装"
            echo "  --dry-run               仅显示将要执行的操作"
            echo "  -h, --help              显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# 系统检查函数
# =============================================================================

perform_system_checks() {
    log_info "开始系统环境检查..."
    
    local check_results=()
    
    # 检查操作系统
    if check_os; then
        check_results+=("操作系统: ✓")
    else
        check_results+=("操作系统: ✗")
    fi
    
    # 检查CPU架构
    if check_cpu; then
        check_results+=("CPU架构: ✓")
    else
        check_results+=("CPU架构: ✗")
    fi
    
    # 检查内存
    if check_memory; then
        check_results+=("内存: ✓")
    else
        check_results+=("内存: ✗")
    fi
    
    # 检查磁盘空间
    if check_disk_space 100; then
        check_results+=("磁盘空间: ✓")
    else
        check_results+=("磁盘空间: ✗")
    fi
    
    # 检查网络连通性
    if check_network_connectivity; then
        check_results+=("网络连通性: ✓")
    else
        check_results+=("网络连通性: ✗")
    fi
    
    # 输出检查结果
    log_info "系统检查结果:"
    for result in "${check_results[@]}"; do
        log_info "  $result"
    done
    
    # 检查是否有失败项
    if printf '%s\n' "${check_results[@]}" | grep -q "✗"; then
        log_error "系统检查发现问题，请解决后重试"
        return 1
    fi
    
    log_info "系统环境检查通过"
    return 0
}

# =============================================================================
# 软件包安装函数
# =============================================================================

install_base_packages() {
    log_info "安装基础软件包..."
    
    local base_packages=(
        "wget" "curl" "rsync" "unzip" "tar" "gzip"
        "git" "vim" "htop" "iotop" "nethogs"
        "net-tools" "telnet" "nc" "tcpdump"
        "lsof" "strace" "sysstat" "dstat"
        "chrony" "logrotate" "crontabs"
    )
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将安装基础软件包: ${base_packages[*]}"
        return 0
    fi
    
    # 更新软件包索引
    log_info "更新软件包索引..."
    yum update -y
    
    # 安装基础软件包
    for package in "${base_packages[@]}"; do
        if ! check_package_installed "localhost" "$package"; then
            log_info "安装软件包: $package"
            yum install -y "$package" || log_warn "软件包 $package 安装失败"
        else
            log_debug "软件包 $package 已安装"
        fi
    done
    
    log_info "基础软件包安装完成"
}

install_development_tools() {
    log_info "安装开发工具..."
    local dev_packages=(
        "gcc" "gcc-c++" "make" "cmake" "autoconf"
        "automake" "libtool" "pkgconfig" "patch" "openssl-devel"
        "zlib-devel" "libcurl-devel" "readline-devel" "ncurses-devel" "libaio-devel"
        "numactl-devel" "jemalloc-devel" "python3" "python3-pip" "python3-devel"
        "binutils" "bison" "flex" "gdb" "gettext"
        "glibc-devel" "openEuler-rpm-config" "pkgconf" "rpm" "rpm-build"
        "asciidoc" "byacc" "ctags" "diffstat" "elfutils"
        "gcc-gfortran" "git" "intltool" "ltrace" "patchutils"
        "perl-Fedora-VSP" "perl-generators" "pesign" "source-highlight" "subversion"
        "systemtap" "valgrind" "valgrind-devel" "babel" "chrpath"
        "expect" "gcc-objc" "gcc-objc++" "mercurial" "mod_dav_svn"
        "rpmdevtools" "rpmlint" "systemtap-sdt-devel" "systemtap-server"
    )
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将安装开发工具: ${dev_packages[*]}"
        return 0
    fi
    
    # 安装开发工具组
#    log_info "安装开发工具组..."
#    yum groupinstall -y "Development Tools" || log_warn "开发工具组安装失败"
    
    # 安装开发相关软件包
    for package in "${dev_packages[@]}"; do
        if ! check_package_installed "localhost" "$package"; then
            log_info "安装开发工具: $package"
            yum install -y "$package" || log_warn "开发工具 $package 安装失败"
        else
            log_debug "开发工具 $package 已安装"
        fi
    done
    
    log_info "开发工具安装完成"
}

install_java_environment() {
    log_info "安装Java环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将安装Java环境"
        return 0
    fi
    
    # 检查是否已安装Java
    if command -v java >/dev/null 2>&1; then
        local java_version=$(java -version 2>&1 | head -1)
        log_info "Java已安装: $java_version"
        
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            return 0
        fi
    fi
    
    # 安装华为毕昇JDK（推荐用于海光CPU）
    local jdk_url="https://mirrors.huaweicloud.com/kunpeng/archive/compiler/bisheng_jdk/bisheng-jdk-11.0.16-linux-x64.tar.gz"
    local jdk_file="/apps/offline-prep/jdk/bisheng-jdk-11.0.16-linux-x64.tar.gz"
    local jdk_dir="/apps/jdk"
    
    log_info "下载华为毕昇JDK..."
    cd "$TEMP_DIR"
    if [[ ! -f "$jdk_file" ]]; then
        log_warn "华为毕昇JDK下载失败，尝试安装OpenJDK"
        yum install -y java-11-openjdk-devel
        return $?
    fi
#    if ! wget "$jdk_url" -O "$jdk_file"; then
#        log_warn "华为毕昇JDK下载失败，尝试安装OpenJDK"
#        yum install -y java-11-openjdk-devel
#        return $?
#    fi
    
    # 解压安装JDK
    log_info "安装JDK到 $jdk_dir"
    mkdir -p "$jdk_dir"
    tar -zxf "$jdk_file" -C "$jdk_dir" --strip-components=1
    
    # 设置环境变量
    cat > /etc/profile.d/java.sh << 'EOF'
export JAVA_HOME=/apps/jdk
export PATH=$JAVA_HOME/bin:$PATH
export CLASSPATH=.:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar
EOF
    
    source /etc/profile.d/java.sh
    
    # 验证安装
    if "$JAVA_HOME/bin/java" -version; then
        log_info "Java环境安装成功"
    else
        log_error "Java环境安装失败"
        return 1
    fi
}

# =============================================================================
# 系统优化函数
# =============================================================================

optimize_kernel_parameters() {
    log_info "优化内核参数..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将优化内核参数"
        return 0
    fi
    
    # 备份原始配置
    cp /etc/sysctl.conf /etc/sysctl.conf.backup_$(date +%Y%m%d_%H%M%S)
    
    # 添加优化参数
    cat >> /etc/sysctl.conf << 'EOF'

# 工业场景基础服务优化参数
# 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000

# 内存优化
vm.swappiness = 1
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.overcommit_memory = 1

# 文件系统优化
fs.file-max = 6815744
fs.nr_open = 1048576

# 共享内存优化
kernel.shmmax = 68719476736
kernel.shmall = 4294967296
kernel.shmmni = 4096
kernel.sem = 250 32000 100 128
EOF
    
    # 应用参数
    sysctl -p
    
    log_info "内核参数优化完成"
}

optimize_limits() {
    log_info "优化系统限制..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将优化系统限制"
        return 0
    fi
    
    # 备份原始配置
    cp /etc/security/limits.conf /etc/security/limits.conf.backup_$(date +%Y%m%d_%H%M%S)
    
    # 添加限制优化
    cat >> /etc/security/limits.conf << 'EOF'

# 工业场景基础服务限制优化
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
* soft stack 32768
* hard stack 32768
* soft memlock unlimited
* hard memlock unlimited
EOF
    
    log_info "系统限制优化完成"
}

disable_unnecessary_services() {
    log_info "禁用不必要的服务..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将禁用不必要的服务"
        return 0
    fi
    
    local services_to_disable=(
        "postfix" "cups" "avahi-daemon" "bluetooth"
        "ModemManager" "NetworkManager-wait-online"
    )
    
    for service in "${services_to_disable[@]}"; do
        if systemctl is-enabled "$service" >/dev/null 2>&1; then
            log_info "禁用服务: $service"
            systemctl disable "$service" || log_warn "禁用服务 $service 失败"
        fi
    done
    
    # 禁用防火墙（如果配置要求）
    if [[ "$ENABLE_FIREWALL" == "false" ]]; then
        log_info "禁用防火墙"
        systemctl stop firewalld || true
        systemctl disable firewalld || true
    fi
    
    # 禁用SELinux（如果配置要求）
    if [[ "$ENABLE_SELINUX" == "false" ]]; then
        log_info "禁用SELinux"
        setenforce 0 || true
        sed -i 's/SELINUX=enforcing/SELINUX=disabled/' /etc/selinux/config || true
    fi
    
    log_info "服务优化完成"
}

configure_time_sync() {
    log_info "配置时间同步..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将配置时间同步"
        return 0
    fi
    
    # 安装chrony
    if ! check_package_installed "localhost" "chrony"; then
        yum install -y chrony
    fi
    
    # 配置NTP服务器
    cat > /etc/chrony.conf << EOF
# NTP服务器配置
server ntp.aliyun.com iburst
server time.windows.com iburst
server cn.pool.ntp.org iburst

# 其他配置
driftfile /var/lib/chrony/drift
makestep 1.0 3
rtcsync
logdir /var/log/chrony
EOF
    
    # 启动并启用chrony服务
    systemctl enable chronyd
    systemctl start chronyd
    
    # 强制同步时间
    chronyd sources -v
    
    log_info "时间同步配置完成"
}

# =============================================================================
# 目录结构创建函数
# =============================================================================

create_directory_structure() {
    log_info "创建目录结构..."
    
    local directories=(
        "$SOFTWARE_REPO"
        "$BACKUP_DIR"
        "$LOG_DIR"
        "$SCRIPT_DIR"
        "/apps/mongodb"
        "/apps/redis"
        "/apps/kafka"
        "/apps/tdengine"
        "/apps/nebula"
        "/apps/dameng"
        "/apps/prometheus"
        "/apps/grafana"
        "/apps/data"
        "/apps/data/mongodb"
        "/apps/data/redis"
        "/apps/data/kafka"
        "/apps/data/tdengine"
        "/apps/data/nebula"
        "/apps/data/dameng"
        "/var/log/deploy"
        "/var/log/mongodb"
        "/var/log/redis"
        "/var/log/kafka"
        "/var/log/tdengine"
        "/var/log/nebula"
        "/var/log/dameng"
    )
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将创建目录: ${directories[*]}"
        return 0
    fi
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_debug "创建目录: $dir"
            mkdir -p "$dir"
            chmod 755 "$dir"
        fi
    done
    
    log_info "目录结构创建完成"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始环境准备..."
    
    # 获取锁
    if ! acquire_lock "prepare_environment"; then
        log_error "无法获取锁，可能有其他实例正在运行"
        exit 1
    fi
    
    # 系统检查
    if [[ "$SKIP_SYSTEM_CHECK" != "true" ]]; then
        if ! perform_system_checks; then
            log_error "系统检查失败"
            exit 1
        fi
    else
        log_warn "跳过系统检查"
    fi
    log_info "清空ssh连接提示"
    echo "" > /etc/motd
    rm -rf /etc/issue /etc/issue.net
    # 创建目录结构
    create_directory_structure
    
    # 软件包安装
    if [[ "$SKIP_PACKAGE_INSTALL" != "true" ]]; then
        install_base_packages
        install_development_tools
        install_java_environment
    else
        log_warn "跳过软件包安装"
    fi
    
    # 系统优化
    if [[ "$SKIP_OPTIMIZATION" != "true" ]]; then
        optimize_kernel_parameters
        optimize_limits
        disable_unnecessary_services
        configure_time_sync
    else
        log_warn "跳过系统优化"
    fi
    
    log_info "环境准备完成"
    
    # 输出摘要信息
    echo
    echo "=== 环境准备摘要 ==="
    echo "操作系统: $(grep "^NAME=" /etc/os-release | cut -d'"' -f2)"
    echo "CPU架构: $(uname -m)"
    echo "内存: $(free -h | awk '/^Mem:/{print $2}')"
    echo "磁盘空间: $(df -h / | awk 'NR==2{print $4}')"
    echo "Java版本: $(java -version 2>&1 | head -1 || echo '未安装')"
    echo "时间同步: $(systemctl is-active chrony)"
    echo "===================="
    
    return 0
}

# 设置脚本权限
chmod +x "$0"

# 执行主函数
main "$@"
