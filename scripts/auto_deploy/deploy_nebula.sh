#!/bin/bash
# NebulaGraph集群部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化NebulaGraph集群部署

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="NebulaGraph集群部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# NebulaGraph特定配置
# =============================================================================

# NebulaGraph配置
NEBULA_USER="nebula"
NEBULA_GROUP="nebula"
NEBULA_HOME="/apps/nebula"
NEBULA_DATA_DIR="/apps/data/nebula"
NEBULA_LOG_DIR="/var/log/nebula"
NEBULA_CONFIG_DIR="$NEBULA_HOME/etc"

# 安装配置
NEBULA_SOURCE_DIR="/apps/software/nebula"
NEBULA_CONSOLE_DIR="/apps/software/nebula-console"
# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_INSTALL=false
SKIP_CONFIG=false
SKIP_INIT=false
FORCE_REINSTALL=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-install      跳过安装"
            echo "  --skip-config       跳过配置生成"
            echo "  --skip-init         跳过集群初始化"
            echo "  --force-reinstall   强制重新安装"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# NebulaGraph安装函数
# =============================================================================

prepare_nebula_environment() {
    local host=$1
    
    log_info "在 $host 上准备NebulaGraph环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备NebulaGraph环境"
        return 0
    fi

    check_offline_environment "$host"

    # 配置离线YUM仓库（如果是离线模式）
    setup_offline_yum_repo "$host"

    # 创建NebulaGraph用户
    remote_execute "$host" "
        if ! id $NEBULA_USER >/dev/null 2>&1; then
            groupadd -g 1006 $NEBULA_GROUP
            useradd -u 1006 -g $NEBULA_GROUP -r -s /bin/false -d /var/lib/nebula $NEBULA_USER
        fi
    "
    
    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $NEBULA_HOME/{bin,etc,lib,share}
        mkdir -p $NEBULA_DATA_DIR/{meta,storage,graph,log,backup}
        mkdir -p $NEBULA_LOG_DIR
        mkdir -p /var/lib/nebula
        mkdir -p /var/run/nebula
        mkdir -p $NEBULA_CONSOLE_DIR
        chown -R $NEBULA_USER:$NEBULA_GROUP $NEBULA_HOME $NEBULA_DATA_DIR $NEBULA_HOME/share
        chown -R $NEBULA_USER:$NEBULA_GROUP $NEBULA_LOG_DIR /var/lib/nebula /var/run/nebula
        chmod 755 $NEBULA_HOME $NEBULA_DATA_DIR $NEBULA_LOG_DIR $NEBULA_HOME/share
        chmod 750 /var/run/nebula
    "
    
    # 安装依赖包
    install_system_packages "$host" \
        "gcc" "gcc-c++" "make" "cmake" "git" \
        "glibc-devel" "libstdc++-devel" "openssl-devel" \
        "readline-devel" "ncurses-devel" "zlib-devel"
    
    log_info "NebulaGraph环境准备完成: $host"
}

install_nebula() {
    local host=$1
    
    log_info "在 $host 上安装NebulaGraph..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上安装NebulaGraph"
        return 0
    fi
    
    # 检查是否已安装
    if remote_execute "$host" "test -f $NEBULA_HOME/bin/nebula-metad" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "NebulaGraph已安装在 $host，跳过安装"
            return 0
        else
            log_info "强制重新安装NebulaGraph在 $host"
        fi
    fi
    
    # 安装NebulaGraph
    remote_execute "$host" "
        cd $NEBULA_SOURCE_DIR
        
        # 查找安装包
        if [[ -f nebula-graph-3.4.0.el7.x86_64.tar.gz ]]; then
            tar -zxf nebula-graph-3.4.0.el7.x86_64.tar.gz
            cp -r nebula-graph-3.4.0.el7.x86_64/* $NEBULA_HOME/
        elif [[ -f nebula-*.rpm ]]; then
            # 使用RPM包安装
            rpm -ivh nebula-*.rpm
        else
            echo '错误: 找不到NebulaGraph安装包'
            exit 1
        fi

        cd $NEBULA_CONSOLE_DIR
        if [[ -f nebula-console-linux-amd64-v3.4.0 ]]; then
          mv nebula-console-linux-amd64-v3.4.0 $NEBULA_HOME/bin/nebula-console
          chmod +x $NEBULA_HOME/bin/nebula-console
        else
          echo '错误: 找不到nebula-console-linux-amd64-v3.4.0包'
          exit 1
        fi

        # 设置权限
        chown -R $NEBULA_USER:$NEBULA_GROUP $NEBULA_HOME
        
        # 设置环境变量
        echo 'export NEBULA_HOME=$NEBULA_HOME' >> /etc/profile
        echo 'export PATH=\$PATH:\$NEBULA_HOME/bin' >> /etc/profile
        
        # 验证安装
        ls -la $NEBULA_HOME/bin/nebula-*
    "
    
    log_info "NebulaGraph安装完成: $host"
}

# =============================================================================
# NebulaGraph配置函数
# =============================================================================

generate_metad_config() {
    local host=$1
    local node_id=$2
    
    log_info "为 $host 生成Meta服务配置文件..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host 生成Meta服务配置文件"
        return 0
    fi
    
    # 构建Meta集群节点列表
    local meta_servers=""
    for nebula_host in "${NEBULA_HOSTS[@]}"; do
        if [[ -n "$meta_servers" ]]; then
            meta_servers+=","
        fi
        meta_servers+="$nebula_host:$NEBULA_META_PORT"
    done
    
    # 生成Meta配置文件
    local metad_config="# NebulaGraph Meta配置文件 - $host
--meta_server_addrs=$meta_servers
--local_ip=$host
--port=$NEBULA_META_PORT
--data_path=$NEBULA_DATA_DIR/meta
--log_dir=$NEBULA_LOG_DIR
--pid_file=/var/run/nebula/nebula-metad.pid
--v=0
--minloglevel=2
--heartbeat_interval_secs=10
--meta_ingest_thread_num=3
--num_io_threads=16
--meta_http_thread_num=3
--num_worker_threads=32
--ws_ip=$host
--ws_http_port=19559
--ws_h2_port=19560"
    
    # 写入配置文件
    remote_execute "$host" "cat > $NEBULA_CONFIG_DIR/nebula-metad.conf << 'EOF'
$metad_config
EOF"
    
    log_info "Meta服务配置文件生成完成: $host"
}

generate_storaged_config() {
    local host=$1
    
    log_info "为 $host 生成Storage服务配置文件..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host 生成Storage服务配置文件"
        return 0
    fi
    
    # 构建Meta集群节点列表
    local meta_servers=""
    for nebula_host in "${NEBULA_HOSTS[@]}"; do
        if [[ -n "$meta_servers" ]]; then
            meta_servers+=","
        fi
        meta_servers+="$nebula_host:$NEBULA_META_PORT"
    done
    
    # 生成Storage配置文件
    local storaged_config="# NebulaGraph Storage配置文件 - $host
--meta_server_addrs=$meta_servers
--local_ip=$host
--port=$NEBULA_STORAGE_PORT
--data_path=$NEBULA_DATA_DIR/storage
--log_dir=$NEBULA_LOG_DIR
--v=0
--pid_file=/var/run/nebula/nebula-storage.pid
--minloglevel=2
--heartbeat_interval_secs=10
--raft_heartbeat_interval_secs=30
--raft_rpc_timeout_ms=5000
--rocksdb_batch_size=4096
--rocksdb_block_cache=4096
--engine_type=rocksdb
--num_io_threads=16
--num_worker_threads=32
--ws_ip=$host
--ws_http_port=19779
--ws_h2_port=19780"
    
    # 写入配置文件
    remote_execute "$host" "cat > $NEBULA_CONFIG_DIR/nebula-storaged.conf << 'EOF'
$storaged_config
EOF"
    
    log_info "Storage服务配置文件生成完成: $host"
}

generate_graphd_config() {
    local host=$1
    
    log_info "为 $host 生成Graph服务配置文件..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host 生成Graph服务配置文件"
        return 0
    fi
    
    # 构建Meta集群节点列表
    local meta_servers=""
    for nebula_host in "${NEBULA_HOSTS[@]}"; do
        if [[ -n "$meta_servers" ]]; then
            meta_servers+=","
        fi
        meta_servers+="$nebula_host:$NEBULA_META_PORT"
    done
    
    # 生成Graph配置文件
    local graphd_config="# NebulaGraph Graph配置文件 - $host
--meta_server_addrs=$meta_servers
--port=$NEBULA_GRAPH_PORT
--log_dir=$NEBULA_LOG_DIR
--v=0
--minloglevel=2
--pid_file=/var/run/nebula/nebula-graphd.pid
--heartbeat_interval_secs=10
--num_netio_threads=4
--num_accept_threads=1
--num_worker_threads=4
--max_sessions_per_ip_per_user=300
--session_reclaim_interval_secs=60
--session_idle_timeout_secs=28800
--local_ip=$host
--ws_ip=$host
--ws_http_port=19669
--ws_h2_port=19670"
    
    # 写入配置文件
    remote_execute "$host" "cat > $NEBULA_CONFIG_DIR/nebula-graphd.conf << 'EOF'
$graphd_config
EOF"
    
    log_info "Graph服务配置文件生成完成: $host"
}

create_nebula_services() {
    local host=$1
    
    log_info "在 $host 上创建NebulaGraph systemd服务..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上创建NebulaGraph服务"
        return 0
    fi
    
    # 创建Meta服务
    local metad_service="[Unit]
Description=NebulaGraph Meta Service
After=network.target

[Service]
WorkingDirectory=$NEBULA_HOME
Type=forking
User=$NEBULA_USER
Group=$NEBULA_GROUP
ExecStart=$NEBULA_HOME/bin/nebula-metad --flagfile=$NEBULA_CONFIG_DIR/nebula-metad.conf --daemonize
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
LimitNOFILE=65536
PIDFile=/var/run/nebula/nebula-metad.pid

[Install]
WantedBy=multi-user.target"
    
    # 创建Storage服务
    local storaged_service="[Unit]
Description=NebulaGraph Storage Service
After=network.target nebula-metad.service
Requires=nebula-metad.service

[Service]
WorkingDirectory=$NEBULA_HOME
Type=forking
User=$NEBULA_USER
Group=$NEBULA_GROUP
ExecStart=$NEBULA_HOME/bin/nebula-storaged --flagfile=$NEBULA_CONFIG_DIR/nebula-storaged.conf --daemonize
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
LimitNOFILE=65536
PIDFile=/var/run/nebula/nebula-storage.pid

[Install]
WantedBy=multi-user.target"
    
    # 创建Graph服务
    local graphd_service="[Unit]
Description=NebulaGraph Graph Service
After=network.target nebula-metad.service
Requires=nebula-metad.service

[Service]
WorkingDirectory=$NEBULA_HOME
Type=forking
User=$NEBULA_USER
Group=$NEBULA_GROUP
ExecStart=$NEBULA_HOME/bin/nebula-graphd --flagfile=$NEBULA_CONFIG_DIR/nebula-graphd.conf --daemonize
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
LimitNOFILE=65536
PIDFile=/var/run/nebula/nebula-graphd.pid

[Install]
WantedBy=multi-user.target"
    
    remote_execute "$host" "
        # 创建Meta服务
        cat > /etc/systemd/system/nebula-metad.service << 'EOF'
$metad_service
EOF
        
        # 创建Storage服务
        cat > /etc/systemd/system/nebula-storaged.service << 'EOF'
$storaged_service
EOF
        
        # 创建Graph服务
        cat > /etc/systemd/system/nebula-graphd.service << 'EOF'
$graphd_service
EOF
        
        systemctl daemon-reload
        systemctl enable nebula-metad
        systemctl enable nebula-storaged
        systemctl enable nebula-graphd
    "
    
    log_info "NebulaGraph服务创建完成: $host"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始NebulaGraph集群部署..."
    
    # 获取锁
    if ! acquire_lock "deploy_nebula"; then
        log_error "无法获取锁，可能有其他NebulaGraph部署实例正在运行"
        exit 1
    fi
    
    # 检查NebulaGraph主机配置
    if [[ ${#NEBULA_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置NebulaGraph主机"
        exit 1
    fi
    
    log_info "NebulaGraph集群主机: ${NEBULA_HOSTS[*]}"

    # 安装阶段
    if [[ "$SKIP_INSTALL" != "true" ]]; then
        for host in "${NEBULA_HOSTS[@]}"; do
            prepare_nebula_environment "$host"
            install_nebula "$host"
        done
    else
        log_warn "跳过NebulaGraph安装"
    fi

    # 配置阶段
    if [[ "$SKIP_CONFIG" != "true" ]]; then
        local node_id=1
        for host in "${NEBULA_HOSTS[@]}"; do
            generate_metad_config "$host" "$node_id"
            generate_storaged_config "$host"
            generate_graphd_config "$host"
            create_nebula_services "$host"
            node_id=$((node_id + 1))
        done
    else
        log_warn "跳过NebulaGraph配置"
    fi

    # 启动和初始化阶段
    if [[ "$SKIP_INIT" != "true" ]]; then
        start_nebula_cluster
        initialize_nebula_cluster
        verify_nebula_cluster
    else
        log_warn "跳过NebulaGraph集群初始化"
    fi

    log_info "NebulaGraph集群部署完成"
}

# =============================================================================
# NebulaGraph集群管理函数
# =============================================================================

start_nebula_cluster() {
    log_info "启动NebulaGraph集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动NebulaGraph集群"
        return 0
    fi

    # 启动Meta服务
    for host in "${NEBULA_HOSTS[@]}"; do
        log_info "启动Meta服务: $host"
        start_service "$host" "nebula-metad"
        sleep 5
        # 等待Meta服务启动
        if ! remote_execute "$host" "systemctl is-active --quiet nebula-metad"; then
            log_error "Meta服务启动失败: $host"
            return 1
        fi
    done

    # 等待Meta集群稳定
    log_info "等待Meta集群稳定..."
    sleep 30

    # 启动Storage服务
    for host in "${NEBULA_HOSTS[@]}"; do
        log_info "启动Storage服务: $host"
        start_service "$host" "nebula-storaged"
        sleep 5
        # 等待Storage服务启动
        if ! remote_execute "$host" "systemctl is-active --quiet nebula-storaged"; then
            log_error "Storage服务启动失败: $host"
            return 1
        fi
    done

    # 等待Storage集群稳定
    log_info "等待Storage集群稳定..."
    sleep 30

    # 启动Graph服务
    for host in "${NEBULA_HOSTS[@]}"; do
        log_info "启动Graph服务: $host"
        start_service "$host" "nebula-graphd"
        sleep 5
        # 等待Graph服务启动
        if ! remote_execute "$host" "systemctl is-active --quiet nebula-graphd"; then
            log_error "Graph服务启动失败: $host"
            return 1
        fi
    done

    log_info "NebulaGraph集群启动完成"
}

initialize_nebula_cluster() {
    log_info "初始化NebulaGraph集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将初始化NebulaGraph集群"
        return 0
    fi

    local first_host="${NEBULA_HOSTS[0]}"

    # 添加Storage主机到集群
    for host in "${NEBULA_HOSTS[@]}"; do
        log_info "添加Storage主机到集群: $host"
        remote_execute "$first_host" "
            $NEBULA_HOME/bin/nebula-console -addr $first_host -port $NEBULA_GRAPH_PORT -u root -p nebula << EOF
ADD HOSTS $host:$NEBULA_STORAGE_PORT;
EOF
        "
        sleep 5
    done

    # 等待Storage主机加入
    log_info "等待Storage主机加入集群..."
    sleep 30

    # 显示集群状态
    remote_execute "$first_host" "
        $NEBULA_HOME/bin/nebula-console -addr $first_host -port $NEBULA_GRAPH_PORT -u root -p nebula << EOF
SHOW HOSTS;
EOF
    "

    log_info "NebulaGraph集群初始化完成"
}

verify_nebula_cluster() {
    log_info "验证NebulaGraph集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将验证NebulaGraph集群"
        return 0
    fi

    local first_host="${NEBULA_HOSTS[0]}"

    # 创建测试图空间
    remote_execute "$first_host" "
        $NEBULA_HOME/bin/nebula-console -addr $first_host -port $NEBULA_GRAPH_PORT -u root -p nebula << EOF
CREATE SPACE IF NOT EXISTS test_space(partition_num=10, replica_factor=3);
USE test_space;
CREATE TAG IF NOT EXISTS person(name string, age int);
CREATE EDGE IF NOT EXISTS like(likeness double);
INSERT VERTEX person(name, age) VALUES \"bob\":(\"Bob\", 10);
INSERT VERTEX person(name, age) VALUES \"lily\":(\"Lily\", 9);
INSERT EDGE like(likeness) VALUES \"bob\"->\"lily\":(0.8);
GO FROM \"bob\" OVER like;
DROP SPACE test_space;
EXIT;
EOF
    "

    # 检查集群状态
    if remote_execute "$first_host" "
        $NEBULA_HOME/bin/nebula-console -addr $first_host -port $NEBULA_GRAPH_PORT -u root -p nebula << EOF
SHOW HOSTS;
EXIT;
EOF
    " | grep -q "ONLINE"; then
        log_info "NebulaGraph集群验证成功"
    else
        log_error "NebulaGraph集群验证失败"
        return 1
    fi

    log_info "NebulaGraph集群验证完成"
}

# 执行主函数
main "$@"
