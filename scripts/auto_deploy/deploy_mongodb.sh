#!/bin/bash
# MongoDB集群部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化MongoDB副本集部署

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="MongoDB集群部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# MongoDB特定配置
# =============================================================================

# MongoDB配置
MONGODB_USER="mongodb"
MONGODB_GROUP="mongodb"
MONGODB_HOME="/apps/mongodb"
MONGODB_DATA_DIR="/apps/data/mongodb"
MONGODB_LOG_DIR="/var/log/mongodb"
MONGODB_CONFIG_DIR="$MONGODB_HOME/conf"

# 编译配置
MONGODB_SOURCE_DIR="/apps/software/mongodb"
MONGODB_BUILD_DIR="/tmp/mongodb-build"

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_COMPILE=false
SKIP_CONFIG=false
SKIP_INIT=false
FORCE_REINSTALL=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-compile)
            SKIP_COMPILE=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-compile      跳过编译安装"
            echo "  --skip-config       跳过配置生成"
            echo "  --skip-init         跳过集群初始化"
            echo "  --force-reinstall   强制重新安装"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# MongoDB编译安装函数
# =============================================================================

prepare_mongodb_environment() {
    local host=$1
    
    log_info "在 $host 上准备MongoDB环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备MongoDB环境"
        return 0
    fi
    
    # 创建MongoDB用户
    remote_execute "$host" "
        if ! id $MONGODB_USER >/dev/null 2>&1; then
            groupadd -g 1001 $MONGODB_GROUP
            useradd -u 1001 -g $MONGODB_GROUP -r -s /bin/false -d /var/lib/mongodb $MONGODB_USER
        fi
    "
    
    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $MONGODB_HOME/{bin,conf,logs}
        mkdir -p $MONGODB_DATA_DIR/{data,log,backup}
        mkdir -p /var/run/mongodb
        chown -R $MONGODB_USER:$MONGODB_GROUP $MONGODB_HOME $MONGODB_DATA_DIR /var/run/mongodb
        chmod 755 $MONGODB_HOME $MONGODB_DATA_DIR
        chmod 750 /var/run/mongodb
    "
    
    # 检查离线环境
    check_offline_environment "$host"

    # 配置离线YUM仓库（如果是离线模式）
    setup_offline_yum_repo "$host"

    # 安装编译依赖
    install_system_packages "$host" \
        "gcc" "gcc-c++" "make" "cmake" "python3" "python3-pip" "python3-devel" \
        "openssl-devel" "libcurl-devel" "zlib-devel" \
        "readline-devel" "ncurses-devel" "libaio-devel" "numactl-devel"

    # 安装Python构建工具
    install_python_packages "$host" "scons==4.4.0"
    
    log_info "MongoDB环境准备完成: $host"
}

# =============================================================================
# MongoDB服务检查函数
# =============================================================================

check_mongodb_installed() {
    local host=$1
    local service_running=false
    local binary_exists=false

    # 检查服务是否运行
    if remote_execute "$host" "systemctl is-active --quiet mongod"; then
        log_info "MongoDB服务已在运行: $host"
        service_running=true
    fi

    # 检查二进制文件是否存在
    if remote_execute "$host" "test -f $MONGODB_HOME/bin/mongod"; then
        log_info "MongoDB二进制文件已存在: $host"
        binary_exists=true
    fi

    # 检查配置文件是否存在
    if remote_execute "$host" "test -f $MONGODB_CONFIG_DIR/mongod.conf"; then
        log_info "MongoDB配置文件已存在: $host"
    fi

    # 如果服务已完整安装
    if [[ "$service_running" == "true" && "$binary_exists" == "true" ]]; then
        return 0  # 已安装
    fi

    return 1  # 未安装
}


compile_mongodb() {
    local host=$1
    
    log_info "在 $host 上编译MongoDB..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上编译MongoDB"
        return 0
    fi
    # 检查是否已安装
    if remote_execute "$host" "test -f $MONGODB_HOME/bin/mongod" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "MongoDB已安装在 $host，跳过编译"
            return 0
        else
            log_info "强制重新编译MongoDB在 $host"
        fi
    fi
    # 解压源码
    remote_execute "$host" "
        cd $MONGODB_SOURCE_DIR
        echo '解压源码'
        # 离线模式优先查找预编译包
        if [[ '$OFFLINE_MODE' == 'true' ]]; then
            if [[ -f mongodb-hygon-precompiled.tar.gz ]]; then
                echo '使用离线预编译MongoDB包'
                tar -zxf mongodb-hygon-precompiled.tar.gz
                cp -r mongodb-hygon-precompiled/* $MONGODB_HOME/
                chown -R $MONGODB_USER:$MONGODB_GROUP $MONGODB_HOME
                echo 'MongoDB离线安装完成'
                exit 0
            fi
        fi

        # 源码编译模式
        if [[ -f mongodb-without-avx.tar.gz ]]; then
            tar -zxf mongodb-without-avx.tar.gz
            cd mongodb-without-avx/mongo
        elif [[ -f mongodb-linux-x86_64-rhel70-4.4.18.tgz ]]; then
             echo '准备解压'
            tar -zxf mongodb-linux-x86_64-rhel70-4.4.18.tgz
            rm -rf $MONGODB_HOME/*
            mv mongodb-linux-x86_64-rhel70-4.4.18/* $MONGODB_HOME
            mkdir $MONGODB_CONFIG_DIR
            cd $MONGODB_HOME
            echo 'export PATH=$PATH:$MONGODB_HOME/bin' >> /etc/profile
            source /etc/profile
            yum localinstall -y /apps/offline-prep/yum-repo/compat-openssl10-1.0.2o-3.el8.x86_64.rpm
        elif [[ -f mongodb-src-*.tar.gz ]]; then
            tar -zxf mongodb-src-*.tar.gz
            cd mongodb-src-*
        else
            echo '错误: 找不到MongoDB源码包或预编译包'
            echo '离线模式请准备: mongodb-hygon-precompiled.tar.gz'
            echo '源码模式请准备: mongodb-without-avx.tar.gz 或 mongodb-src-*.tar.gz'
            exit 1
        fi

        # 应用无AVX补丁（如果存在）
        if [[ -f ../o2_patch.diff ]]; then
            patch -p1 < ../o2_patch.diff
        fi
    "
    #        if ! grep -q "export PATH=\$PATH:"$MONGODB_HOME"/bin" /etc/profile; then
#            # 使用重定向追加内容
#            echo 'export PATH=$PATH:$MONGODB_HOME/bin' >> /etc/profile
#            echo "配置已成功添加到/etc/profile"
#            source /etc/profile
#            yum localinstall /apps/offline-prep/yum-repo/compat-openssl10-1.0.2o-3.el8.x86_64.rpm
#        else
#            echo "配置已存在,无需重复添加"
#        fi
    # 设置编译环境
#    remote_execute "$host" "
#        export CC=gcc
#        export CXX=g++
#        export CFLAGS='-O2 -march=x86-64 -mtune=generic'
#        export CXXFLAGS='-O2 -march=x86-64 -mtune=generic'
#        export PORTABLE=1
#        export USE_SSE=OFF
#        export USE_AVX=OFF
#        source /etc/profile


#        cd $MONGODB_SOURCE_DIR/mongodb-*/mongo/
#
#        # 安装Python依赖
#        if [[ -f etc/pip/compile-requirements.txt ]]; then
#            pip3 install -r etc/pip/compile-requirements.txt
#        fi
#        python3 buildscripts/scons.py --disable-warnings-as-errors --opt=on --allocator=system --use-system-zlib --use-system-pcre -j\$(nproc) install-mongod
#        # 编译MongoDB
#        python3 buildscripts/scons.py \\
#            --disable-warnings-as-errors \\
#            --opt=on \\
#            --allocator=system \\
#            --use-system-zlib \\
#            --use-system-pcre \\
#            -j\$(nproc) \\
#            install-mongod
#
#        # 安装到目标目录
#        python3 buildscripts/scons.py \\
#            DESTDIR=$MONGODB_HOME \\
#            --prefix=/usr/local \\
#            install-mongod
#
#        # 创建符号链接
#        ln -sf $MONGODB_HOME/usr/local/bin/* $MONGODB_HOME/bin/ || true
#
#    "
#
    # 验证安装
    if remote_execute "$host" "$MONGODB_HOME/bin/mongod --version"; then
        log_info "MongoDB编译安装成功: $host"
    else
        log_error "MongoDB编译安装失败: $host"
        return 1
    fi
}

# ============================================= ================================
# MongoDB配置函数
# =============================================================================

generate_mongodb_config() {
    local host=$1
    local role=${MONGODB_ROLES[$host]:-"secondary"}
    local priority=${MONGODB_PRIORITIES[$host]:-"1"}
    
    log_info "为 $host 生成MongoDB配置文件 (角色: $role)..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host 生成MongoDB配置文件"
        return 0
    fi
    
    # 生成主配置文件
    local config_content="# MongoDB配置文件 - $host
systemLog:
  destination: file
  path: $MONGODB_DATA_DIR/log/mongod.log
  logAppend: true
  logRotate: reopen
  timeStampFormat: iso8601-local

storage:
  dbPath: $MONGODB_DATA_DIR/data
  journal:
    enabled: true
    commitIntervalMs: 100
  wiredTiger:
    engineConfig:
      cacheSizeGB: 16
      journalCompressor: snappy
      directoryForIndexes: true
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

processManagement:
  fork: true
  pidFilePath: /var/run/mongodb/mongod.pid
  timeZoneInfo: /usr/share/zoneinfo

net:
  bindIp: 0.0.0.0
  port: $MONGODB_PORT
  maxIncomingConnections: 2000
  wireObjectCheck: true
  ipv6: false

replication:
  replSetName: \"$MONGODB_REPLICA_SET\"
  oplogSizeMB: $MONGODB_OPLOG_SIZE

security:
  authorization: disabled


operationProfiling:
  slowOpThresholdMs: 100
  mode: slowOp

setParameter:
  enableLocalhostAuthBypass: false
  authenticationMechanisms: SCRAM-SHA-1,SCRAM-SHA-256
  maxLogSizeKB: 10240
  logLevel: 1
  cursorTimeoutMillis: 600000
  notablescan: false
"
#     keyFile: $MONGODB_CONFIG_DIR/mongodb-keyfile
    # 写入配置文件
    remote_execute "$host" "cat > $MONGODB_CONFIG_DIR/mongod.conf << 'EOF'
$config_content
EOF"
    
    # 生成密钥文件
    if [[ "$role" == "primary" ]]; then
        log_info "生成MongoDB密钥文件..."
        local keyfile_content=$(openssl rand -base64 756)
        
        remote_execute "$host" "
            echo '$keyfile_content' > $MONGODB_CONFIG_DIR/mongodb-keyfile
            chmod 400 $MONGODB_CONFIG_DIR/mongodb-keyfile
            chown $MONGODB_USER:$MONGODB_GROUP $MONGODB_CONFIG_DIR/mongodb-keyfile
        "
        
        # 分发密钥文件到其他节点
        for other_host in "${MONGODB_HOSTS[@]}"; do
            if [[ "$other_host" != "$host" ]]; then
                log_debug "分发密钥文件到 $other_host"
                remote_execute "$host" "scp $MONGODB_CONFIG_DIR/mongodb-keyfile $other_host:$MONGODB_CONFIG_DIR/"
                remote_execute "$other_host" "
                    chmod 400 $MONGODB_CONFIG_DIR/mongodb-keyfile
                    chown $MONGODB_USER:$MONGODB_GROUP $MONGODB_CONFIG_DIR/mongodb-keyfile
                "
            fi
        done
    fi
    
    log_info "MongoDB配置文件生成完成: $host"
}

create_mongodb_service() {
    local host=$1
    
    log_info "在 $host 上创建MongoDB systemd服务..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上创建MongoDB服务"
        return 0
    fi
    
    local service_content="[Unit]
Description=MongoDB Database Server
Documentation=https://docs.mongodb.org/manual
After=network-online.target
Wants=network-online.target

[Service]
User=$MONGODB_USER
Group=$MONGODB_GROUP
Type=forking
PIDFile=/var/run/mongodb/mongod.pid
ExecStart=$MONGODB_HOME/bin/mongod --config $MONGODB_CONFIG_DIR/mongod.conf
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mongod
KillMode=mixed
TimeoutStopSec=30
LimitNOFILE=64000
LimitNPROC=64000

[Install]
WantedBy=multi-user.target"
    
    remote_execute "$host" "
        cat > /etc/systemd/system/mongod.service << 'EOF'
$service_content
EOF
        systemctl daemon-reload
        systemctl enable mongod
    "
    
    log_info "MongoDB服务创建完成: $host"
}

# =============================================================================
# MongoDB集群初始化函数
# =============================================================================

start_mongodb_cluster() {
    log_info "启动MongoDB集群..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动MongoDB集群"
        return 0
    fi
    
    # 启动所有节点
    for host in "${MONGODB_HOSTS[@]}"; do
        log_info "启动MongoDB服务: $host"
        start_service "$host" "mongod"
        
        # 等待服务启动
        if ! wait_for_port "$host" "$MONGODB_PORT" 60; then
            log_error "MongoDB服务启动失败: $host"
            return 1
        fi
    done
    
    log_info "所有MongoDB节点启动完成"
}

initialize_replica_set() {
    log_info "初始化MongoDB副本集..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将初始化MongoDB副本集"
        return 0
    fi
    
    # 找到主节点
    local primary_host=""
    for host in "${MONGODB_HOSTS[@]}"; do
        if [[ "${MONGODB_ROLES[$host]}" == "primary" ]]; then
            primary_host="$host"
            break
        fi
    done
    
    if [[ -z "$primary_host" ]]; then
        primary_host="${MONGODB_HOSTS[0]}"
        log_warn "未找到指定的主节点，使用第一个节点: $primary_host"
    fi
    
    # 生成副本集初始化脚本
    local init_script="rs.initiate({
  _id: \"$MONGODB_REPLICA_SET\",
  members: ["
    
    local member_id=0
    for host in "${MONGODB_HOSTS[@]}"; do
        local priority="${MONGODB_PRIORITIES[$host]:-1}"
        local role="${MONGODB_ROLES[$host]:-secondary}"
        
        init_script="$init_script
    { 
      _id: $member_id, 
      host: \"$host:$MONGODB_PORT\", 
      priority: $priority,
      tags: { \"role\": \"$role\", \"datacenter\": \"dc1\" }
    },"
        
        member_id=$((member_id + 1))
    done
    
    # 移除最后的逗号并完成脚本
    init_script="${init_script%,}
  ],
  settings: {
    chainingAllowed: false,
    heartbeatIntervalMillis: 2000,
    heartbeatTimeoutSecs: 10,
    electionTimeoutMillis: 10000,
    catchUpTimeoutMillis: 60000,
    getLastErrorModes: {
      \"majority\": { \"datacenter\": 1 }
    }
  }
})"
    
    # 执行初始化
    log_info "在主节点 $primary_host 上执行副本集初始化..."
    remote_execute "$primary_host" "
        $MONGODB_HOME/bin/mongo --host $primary_host:$MONGODB_PORT --eval '$init_script'
    "
    
    # 等待副本集稳定
    log_info "等待副本集稳定..."
    sleep 30
    
    # 验证副本集状态
    if remote_execute "$primary_host" "$MONGODB_HOME/bin/mongo --host $primary_host:$MONGODB_PORT --eval 'rs.status()'"; then
        log_info "MongoDB副本集初始化成功"
    else
        log_error "MongoDB副本集初始化失败"
        return 1
    fi
}

create_mongodb_users() {
    log_info "创建MongoDB用户..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将创建MongoDB用户"
        return 0
    fi
    
    # 找到主节点
    local primary_host=""
    for host in "${MONGODB_HOSTS[@]}"; do
        if [[ "${MONGODB_ROLES[$host]}" == "primary" ]]; then
            primary_host="$host"
            break
        fi
    done
    
    if [[ -z "$primary_host" ]]; then
        primary_host="${MONGODB_HOSTS[0]}"
    fi
    
    # 生成随机密码
    local admin_password=$(generate_password)
    local monitor_password=$(generate_password)
    
    # 创建管理员用户
    remote_execute "$primary_host" "
        $MONGODB_HOME/bin/mongo --host $primary_host:$MONGODB_PORT --eval '
        db.createUser({
          user: \"admin\",
          pwd: \"$admin_password\",
          roles: [
            { role: \"userAdminAnyDatabase\", db: \"admin\" },
            { role: \"readWriteAnyDatabase\", db: \"admin\" },
            { role: \"dbAdminAnyDatabase\", db: \"admin\" },
            { role: \"clusterAdmin\", db: \"admin\" }
          ]
        })
        
        db.createUser({
          user: \"monitor\",
          pwd: \"$monitor_password\",
          roles: [
            { role: \"clusterMonitor\", db: \"admin\" },
            { role: \"read\", db: \"local\" }
          ]
        })
        '
    "
#    bin/mongo --host ***************:27017 --eval "use admin; db.createUser({ user: 'admin', pwd: '123123', roles: [ { role: 'userAdminAnyDatabase', db: 'admin' }, { role: 'readWriteAnyDatabase', db: 'admin' }, { role: 'dbAdminAnyDatabase', db: 'admin' }, { role: 'clusterAdmin', db: 'admin' } ] }); db.createUser({ user: 'monitor', pwd: '1231231', roles: [ { role: 'clusterMonitor', db: 'admin' }, { role: 'read', db: 'local' } ] });"
    # 保存密码到文件
    remote_execute "$primary_host" "
        cat > $MONGODB_CONFIG_DIR/passwords.txt << EOF
MongoDB管理员密码:
用户名: admin
密码: $admin_password

MongoDB监控用户密码:
用户名: monitor
密码: $monitor_password

连接字符串:
mongodb://admin:$admin_password@${MONGODB_HOSTS[0]}:$MONGODB_PORT,${MONGODB_HOSTS[1]}:$MONGODB_PORT,${MONGODB_HOSTS[2]}:$MONGODB_PORT/admin?replicaSet=$MONGODB_REPLICA_SET
EOF
        chmod 600 $MONGODB_CONFIG_DIR/passwords.txt
        chown $MONGODB_USER:$MONGODB_GROUP $MONGODB_CONFIG_DIR/passwords.txt
    "
    
    log_info "MongoDB用户创建完成，密码已保存到 $primary_host:$MONGODB_CONFIG_DIR/passwords.txt"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始MongoDB集群部署..."
    
    # 获取锁
    if ! acquire_lock "deploy_mongodb"; then
        log_error "无法获取锁，可能有其他MongoDB部署实例正在运行"
        exit 1
    fi
    
    # 检查MongoDB主机配置
    if [[ ${#MONGODB_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置MongoDB主机"
        exit 1
    fi
    
    log_info "MongoDB集群主机: ${MONGODB_HOSTS[*]}"

     # 检查所有节点是否已安装MongoDB
    local all_installed=true
    for host in "${MONGODB_HOSTS[@]}"; do
        if ! check_mongodb_installed "$host"; then
            all_installed=false
            log_info "$host 节点未检测到完整MongoDB安装"
        fi
    done

    # 如果所有节点已安装且未强制重装
    if [[ "$all_installed" == "true" && "$FORCE_REINSTALL" != "true" ]]; then
        log_info "所有节点MongoDB服务已存在，跳过部署"

        # 检查副本集是否已初始化
        local primary_host="${MONGODB_HOSTS[0]}"
        if remote_execute "$primary_host" "$MONGODB_HOME/bin/mongo --host $primary_host:$MONGODB_PORT --eval 'rs.status().ok' | grep -q 1"; then
            log_info "副本集已初始化，跳过集群初始化"
        else
            log_warn "副本集未初始化，将执行初始化步骤"
            start_mongodb_cluster
            initialize_replica_set
            create_mongodb_users
        fi

        release_lock "deploy_mongodb"
        exit 0
    fi

    # 编译安装阶段
    if [[ "$SKIP_COMPILE" != "true" ]]; then
        for host in "${MONGODB_HOSTS[@]}"; do
            prepare_mongodb_environment "$host"
            compile_mongodb "$host"
        done
    else
        log_warn "跳过MongoDB编译安装"
    fi
    
    # 配置阶段
    if [[ "$SKIP_CONFIG" != "true" ]]; then
        for host in "${MONGODB_HOSTS[@]}"; do
            generate_mongodb_config "$host"
            create_mongodb_service "$host"
        done
    else
        log_warn "跳过MongoDB配置"
    fi
    
    # 集群初始化阶段
    if [[ "$SKIP_INIT" != "true" ]]; then
        start_mongodb_cluster
        initialize_replica_set
        create_mongodb_users
    else
        log_warn "跳过MongoDB集群初始化"
    fi
    
    log_info "MongoDB集群部署完成！"
    
    # 输出连接信息
    echo
    echo "=== MongoDB集群信息 ==="
    echo "副本集名称: $MONGODB_REPLICA_SET"
    echo "主机列表: ${MONGODB_HOSTS[*]}"
    echo "端口: $MONGODB_PORT"
    echo "配置文件: $MONGODB_CONFIG_DIR/mongod.conf"
    echo "密码文件: $MONGODB_CONFIG_DIR/passwords.txt"
    echo "========================"
    
    return 0
}

# 设置脚本权限
chmod +x "$0"

# 执行主函数
main "$@"
