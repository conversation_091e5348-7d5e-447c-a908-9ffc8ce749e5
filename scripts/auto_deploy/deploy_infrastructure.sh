#!/bin/bash
# 基础环境部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化基础环境配置

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="基础环境部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_SYSTEM_CONFIG=false
SKIP_SECURITY_CONFIG=false
SKIP_NETWORK_CONFIG=false
FORCE_REINSTALL=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-system)
            SKIP_SYSTEM_CONFIG=true
            shift
            ;;
        --skip-security)
            SKIP_SECURITY_CONFIG=true
            shift
            ;;
        --skip-network)
            SKIP_NETWORK_CONFIG=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-system       跳过系统配置"
            echo "  --skip-security     跳过安全配置"
            echo "  --skip-network      跳过网络配置"
            echo "  --force-reinstall   强制重新配置"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# 系统基础配置函数
# =============================================================================

configure_system_basics() {
    local host=$1
    
    log_info "在 $host 上配置系统基础环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上配置系统基础环境"
        return 0
    fi
    
    # 设置时区
    remote_execute "$host" "
        timedatectl set-timezone $TIMEZONE
        log_info '时区设置为: $TIMEZONE'
    "
    
    # 设置语言环境
    remote_execute "$host" "
        localectl set-locale LANG=$LOCALE
        echo 'export LANG=$LOCALE' >> /etc/profile
        log_info '语言环境设置为: $LOCALE'
    "
    
    # 检查离线环境
    check_offline_environment "$host"

    # 配置离线YUM仓库（如果是离线模式）
    setup_offline_yum_repo "$host"

    # 配置NTP时间同步
    install_system_packages "$host" "chrony"
    remote_execute "$host" "
        systemctl enable chronyd
        
        # 配置NTP服务器
        if [[ '$OFFLINE_MODE' == 'true' ]]; then
            # 离线模式配置
            cat > /etc/chrony.conf << 'EOF'
# NTP服务器配置 - 离线模式
# 使用本地时钟作为时间源
local stratum 8

# 允许本地网络同步
allow ***********/16
allow 10.0.0.0/8
allow **********/12

# 日志配置
logdir /var/log/chrony

# 离线模式配置
makestep 1.0 3
rtcsync
EOF
        else
            # 在线模式配置
            cat > /etc/chrony.conf << 'EOF'
# NTP服务器配置 - 在线模式
server ntp.aliyun.com iburst
server time.windows.com iburst
server cn.pool.ntp.org iburst

# 允许本地网络同步
allow ***********/16
allow 10.0.0.0/8

# 本地时钟配置
local stratum 10

# 日志配置
logdir /var/log/chrony
EOF
        fi
        
        systemctl restart chronyd
        chrony sources -v
        log_info 'NTP时间同步配置完成'
    "
    
    log_info "系统基础配置完成: $host"
}

install_basic_packages() {
    local host=$1
    
    log_info "在 $host 上安装基础软件包..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上安装基础软件包"
        return 0
    fi
    
    # 更新系统（仅在线模式）
    if [[ "$OFFLINE_MODE" != "true" ]]; then
        remote_execute "$host" "
            yum update -y
            yum install -y epel-release
        "
    else
        log_info "离线模式跳过系统更新"
    fi
    
    # 安装基础工具
    install_system_packages "$host" \
        "wget" "curl" "vim" "git" "htop" "iotop" "iftop" "nload" \
        "net-tools" "telnet" "nc" "tcpdump" "wireshark-cli" \
        "rsync" "screen" "tmux" "tree" "lsof" "strace" \
        "gcc" "gcc-c++" "make" "cmake" "autoconf" "automake" \
        "openssl-devel" "zlib-devel" "pcre-devel" "libtool" \
        "python3" "python3-pip" "python3-devel" \
        "java-11-openjdk" "java-11-openjdk-devel"
    
    # 安装监控工具
    install_system_packages "$host" \
        "sysstat" "dstat" "iostat" "vmstat" "mpstat" \
        "perf" "numactl" "numactl-devel"
    
    log_info "基础软件包安装完成: $host"
}

configure_security() {
    local host=$1

    log_info "在 $host 上配置安全设置..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上配置安全设置"
        return 0
    fi

    # 配置防火墙
    if [[ "$ENABLE_FIREWALL" == "true" ]]; then
        remote_execute "$host" "
            systemctl enable firewalld
            systemctl start firewalld

            # 开放必要端口
            firewall-cmd --permanent --add-port=$SSH_PORT/tcp
            firewall-cmd --permanent --add-port=$MONGODB_PORT/tcp
            firewall-cmd --permanent --add-port=$REDIS_CLUSTER_PORTS/tcp
            firewall-cmd --permanent --add-port=$KAFKA_PORT/tcp
            firewall-cmd --permanent --add-port=$ZOOKEEPER_PORT/tcp
            firewall-cmd --permanent --add-port=$TDENGINE_PORT/tcp
            firewall-cmd --permanent --add-port=$NEBULA_GRAPH_PORT/tcp
            firewall-cmd --permanent --add-port=$NEBULA_META_PORT/tcp
            firewall-cmd --permanent --add-port=$NEBULA_STORAGE_PORT/tcp
            firewall-cmd --permanent --add-port=$DAMENG_PORT/tcp
            firewall-cmd --permanent --add-port=$PROMETHEUS_PORT/tcp
            firewall-cmd --permanent --add-port=$GRAFANA_PORT/tcp
            firewall-cmd --permanent --add-port=$ALERTMANAGER_PORT/tcp

            firewall-cmd --reload
            log_info '防火墙配置完成'
        "
    else
        remote_execute "$host" "
            systemctl disable firewalld
            systemctl stop firewalld
            log_info '防火墙已禁用'
        "
    fi

    # 配置SELinux
    if [[ "$ENABLE_SELINUX" == "true" ]]; then
        remote_execute "$host" "
            setenforce 1
            sed -i 's/SELINUX=.*/SELINUX=enforcing/' /etc/selinux/config
            log_info 'SELinux已启用'
        "
    else
        remote_execute "$host" "
            setenforce 0
            sed -i 's/SELINUX=.*/SELINUX=disabled/' /etc/selinux/config
            log_info 'SELinux已禁用'
        "
    fi

    # 配置SSH安全
    remote_execute "$host" "
        # 备份SSH配置
        cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

        # 配置SSH安全参数
        cat > /etc/ssh/sshd_config.d/99-security.conf << 'EOF'
# SSH安全配置
Port $SSH_PORT
PermitRootLogin yes
PasswordAuthentication no
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
Protocol 2
EOF

        systemctl restart sshd
        log_info 'SSH安全配置完成'
    "

    log_info "安全配置完成: $host"
}

optimize_storage() {
    local host=$1

    log_info "在 $host 上优化存储配置..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上优化存储配置"
        return 0
    fi

    # 检查数据盘
    remote_execute "$host" "
        if [[ -b '$DATA_DISK' ]]; then
            # 创建文件系统
            if ! blkid '$DATA_DISK' | grep -q '$FILESYSTEM_TYPE'; then
                mkfs.$FILESYSTEM_TYPE '$DATA_DISK'
                log_info '数据盘文件系统创建完成: $FILESYSTEM_TYPE'
            fi

            # 创建挂载点
            mkdir -p '$DATA_MOUNT_POINT'

            # 配置自动挂载
            if ! grep -q '$DATA_DISK' /etc/fstab; then
                echo '$DATA_DISK $DATA_MOUNT_POINT $FILESYSTEM_TYPE defaults,noatime 0 2' >> /etc/fstab
            fi

            # 挂载数据盘
            mount '$DATA_MOUNT_POINT' || true

            # 设置权限
            chmod 755 '$DATA_MOUNT_POINT'

            log_info '数据盘配置完成: $DATA_DISK -> $DATA_MOUNT_POINT'
        else
            log_warn '数据盘不存在: $DATA_DISK'
        fi
    "

    # 优化文件系统参数
    remote_execute "$host" "
        # 创建数据目录结构
        mkdir -p $DATA_MOUNT_POINT/{mongodb,redis,kafka,tdengine,nebula,dameng,backup,logs}

        # 设置目录权限
        chmod 755 $DATA_MOUNT_POINT/*

        log_info '数据目录结构创建完成'
    "

    log_info "存储优化完成: $host"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始基础环境部署..."
    
    # 获取锁
    if ! acquire_lock "deploy_infrastructure"; then
        log_error "无法获取锁，可能有其他基础环境部署实例正在运行"
        exit 1
    fi
    
    # 获取所有主机列表
    local all_hosts=()
    all_hosts+=("${MONGODB_HOSTS[@]}")
    all_hosts+=("${REDIS_HOSTS[@]}")
    all_hosts+=("${KAFKA_HOSTS[@]}")
    all_hosts+=("${TDENGINE_HOSTS[@]}")
    all_hosts+=("${NEBULA_HOSTS[@]}")
    all_hosts+=("${DAMENG_HOSTS[@]}")
    all_hosts+=("${MONITOR_HOSTS[@]}")
    
    # 去重
    local unique_hosts=($(printf "%s\n" "${all_hosts[@]}" | sort -u))
    
    log_info "将在以下主机上部署基础环境: ${unique_hosts[*]}"
    
    # 系统配置阶段
    if [[ "$SKIP_SYSTEM_CONFIG" != "true" ]]; then
        for host in "${unique_hosts[@]}"; do
            configure_system_basics "$host"
            install_basic_packages "$host"
            optimize_storage "$host"
        done
    else
        log_warn "跳过系统配置"
    fi

    # 安全配置阶段
    if [[ "$SKIP_SECURITY_CONFIG" != "true" ]]; then
        for host in "${unique_hosts[@]}"; do
            configure_security "$host"
        done
    else
        log_warn "跳过安全配置"
    fi

    # 网络配置阶段
    if [[ "$SKIP_NETWORK_CONFIG" != "true" ]]; then
        log_info "执行网络配置..."
        if [[ -f "$SCRIPT_DIR/configure_network.sh" ]]; then
            "$SCRIPT_DIR/configure_network.sh" "${unique_hosts[@]}"
        else
            log_warn "网络配置脚本不存在，跳过网络配置"
        fi
    else
        log_warn "跳过网络配置"
    fi

    # 验证部署结果
    log_info "验证基础环境部署结果..."
    local failed_hosts=()

    for host in "${unique_hosts[@]}"; do
        log_info "验证主机: $host"

        # 检查SSH连接
        if ! check_ssh_connection "$host"; then
            log_error "SSH连接失败: $host"
            failed_hosts+=("$host")
            continue
        fi

        # 检查基础服务
        if ! remote_execute "$host" "systemctl is-active chronyd" >/dev/null 2>&1; then
            log_error "时间同步服务未运行: $host"
            failed_hosts+=("$host")
        fi

        # 检查存储挂载
        if ! remote_execute "$host" "mountpoint -q $DATA_MOUNT_POINT" >/dev/null 2>&1; then
            log_warn "数据盘未挂载: $host"
        fi

        log_info "主机验证完成: $host"
    done

    if [[ ${#failed_hosts[@]} -gt 0 ]]; then
        log_error "以下主机部署失败: ${failed_hosts[*]}"
        exit 1
    fi

    log_info "基础环境部署完成，所有主机验证通过"
}

# 执行主函数
main "$@"
