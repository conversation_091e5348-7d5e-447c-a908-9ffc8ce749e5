#!/bin/bash
# 达梦数据库集群部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化达梦数据库集群部署

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="达梦数据库集群部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 达梦数据库特定配置
# =============================================================================

# 达梦数据库配置
DM_USER="dmdba"
DM_GROUP="dinstall"
DM_HOME="/apps/dmdbms"
DM_DATA_DIR="/apps/data/dmdbms"
DM_LOG_DIR="/var/log/dmdbms"
DM_ARCH_DIR="/apps/data/dmdbms/arch"

# 安装配置
DM_SOURCE_DIR="/apps/software/dameng"
DM_INSTALL_DIR="/apps/dmdbms"

# 数据库实例配置
DM_INSTANCE_NAME="DMSERVER"
DM_DB_NAME="DAMENG"

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_INSTALL=false
SKIP_CONFIG=false
SKIP_INIT=false
FORCE_REINSTALL=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-install      跳过安装"
            echo "  --skip-config       跳过配置生成"
            echo "  --skip-init         跳过数据库初始化"
            echo "  --force-reinstall   强制重新安装"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# 达梦数据库安装函数
# =============================================================================

prepare_dameng_environment() {
    local host=$1
    
    log_info "在 $host 上准备达梦数据库环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备达梦数据库环境"
        return 0
    fi
    
    # 创建达梦数据库用户和组
    remote_execute "$host" "
        # 创建dinstall组
        if ! getent group $DM_GROUP >/dev/null 2>&1; then
            groupadd -g 1004 $DM_GROUP
        fi
        
        # 创建dmdba用户
        if ! id $DM_USER >/dev/null 2>&1; then
            useradd -u 1004 -g $DM_GROUP -d /home/<USER>/bin/bash $DM_USER
        fi
        
        # 设置用户密码
        echo '$DM_USER:$(generate_password)' | chpasswd
    "
    
    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $DM_HOME
        mkdir -p $DM_DATA_DIR/{data,log,backup,arch}
        mkdir -p $DM_LOG_DIR
        mkdir -p /home/<USER>/{scripts,backup}
        
        chown -R $DM_USER:$DM_GROUP $DM_HOME $DM_DATA_DIR $DM_LOG_DIR
        chown -R $DM_USER:$DM_GROUP /home/<USER>
        chmod 755 $DM_HOME $DM_DATA_DIR $DM_LOG_DIR
    "
    
    # 配置系统参数
    remote_execute "$host" "
        # 内核参数优化
        cat >> /etc/sysctl.d/99-dameng.conf << 'EOF'
# 达梦数据库内核参数优化
kernel.shmmax = 68719476736
kernel.shmall = 4294967296
kernel.shmmni = 4096
kernel.sem = 250 32000 100 128
fs.aio-max-nr = 1048576
fs.file-max = 6815744
net.ipv4.ip_local_port_range = 9000 65500
net.core.rmem_default = 262144
net.core.rmem_max = 4194304
net.core.wmem_default = 262144
net.core.wmem_max = 1048576
EOF
        
        sysctl -p /etc/sysctl.d/99-dameng.conf
        
        # 用户限制配置
        cat >> /etc/security/limits.d/99-dameng.conf << 'EOF'
# 达梦数据库用户限制
$DM_USER soft nproc 2047
$DM_USER hard nproc 16384
$DM_USER soft nofile 1024
$DM_USER hard nofile 65536
$DM_USER soft stack 10240
$DM_USER hard stack 32768
EOF
    "
    
    log_info "达梦数据库环境准备完成: $host"
}

install_dameng_database() {
    local host=$1
    
    log_info "在 $host 上安装达梦数据库..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上安装达梦数据库"
        return 0
    fi
    
    # 检查是否已安装
    if remote_execute "$host" "test -f $DM_HOME/bin/dmserver" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "达梦数据库已安装在 $host，跳过安装"
            return 0
        else
            log_info "强制重新安装达梦数据库在 $host"
        fi
    fi
    
    # 安装达梦数据库
    remote_execute "$host" "
        cd $DM_SOURCE_DIR
        
        # 查找安装包
        if [[ -f dm8_*.iso ]]; then
            # 挂载ISO文件
            mkdir -p /mnt/dm8
            mount -o loop dm8_*.iso /mnt/dm8
            
            # 静默安装
            /mnt/dm8/DMInstall.bin -i silent -DINSTALL_PATH=$DM_HOME
            
            # 卸载ISO
            umount /mnt/dm8
            rmdir /mnt/dm8
        elif [[ -f dm8_*.tar.gz ]]; then
            # 解压安装
            tar -zxf dm8_*.tar.gz
            cd dm8_*
            ./DMInstall.bin -i silent -DINSTALL_PATH=$DM_HOME
        else
            echo '错误: 找不到达梦数据库安装包'
            exit 1
        fi
        
        # 设置权限
        chown -R $DM_USER:$DM_GROUP $DM_HOME
        
        # 设置环境变量
        cat >> /home/<USER>/.bashrc << 'EOF'
export DM_HOME=$DM_HOME
export PATH=\$DM_HOME/bin:\$PATH
export LD_LIBRARY_PATH=\$DM_HOME/bin:\$LD_LIBRARY_PATH
EOF
        
        # 验证安装
        su - $DM_USER -c '$DM_HOME/bin/dmserver --version'
    "
    
    log_info "达梦数据库安装完成: $host"
}

# =============================================================================
# 数据库配置函数
# =============================================================================

create_database_instance() {
    local host=$1
    local role=${2:-"primary"}  # primary, standby
    
    log_info "在 $host 上创建数据库实例 (角色: $role)..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上创建数据库实例"
        return 0
    fi
    
    # 生成数据库初始化脚本
    local init_script="#!/bin/bash
# 达梦数据库初始化脚本
cd $DM_HOME/bin

# 创建数据库实例
./dminit \\
    PATH=$DM_DATA_DIR/data \\
    CTL_PATH=$DM_DATA_DIR/data \\
    LOG_PATH=$DM_DATA_DIR/log \\
    EXTENT_SIZE=32 \\
    PAGE_SIZE=32 \\
    LOG_SIZE=2048 \\
    CASE_SENSITIVE=Y \\
    CHARSET=1 \\
    DB_NAME=$DM_DB_NAME \\
    INSTANCE_NAME=$DM_INSTANCE_NAME \\
    PORT_NUM=$DAMENG_PORT \\
    SYSDBA_PWD=$(generate_password)
"
    
    # 执行数据库初始化
    remote_execute "$host" "
        # 创建初始化脚本
        cat > /home/<USER>/init_db.sh << 'EOF'
$init_script
EOF
        
        chmod +x /home/<USER>/init_db.sh
        chown $DM_USER:$DM_GROUP /home/<USER>/init_db.sh
        
        # 以dmdba用户执行初始化
        su - $DM_USER -c '/home/<USER>/init_db.sh'
    "
    
    log_info "数据库实例创建完成: $host"
}

configure_dameng_service() {
    local host=$1
    
    log_info "在 $host 上配置达梦数据库服务..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上配置达梦数据库服务"
        return 0
    fi
    
    # 创建systemd服务文件
    local service_content="[Unit]
Description=DM Database Server
After=network.target

[Service]
Type=forking
User=$DM_USER
Group=$DM_GROUP
Environment=DM_HOME=$DM_HOME
ExecStart=$DM_HOME/bin/dmserver $DM_DATA_DIR/data/DAMENG/dm.ini
ExecStop=$DM_HOME/bin/dmserver $DM_DATA_DIR/data/DAMENG/dm.ini stop
Restart=always
RestartSec=10
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target"
    
    remote_execute "$host" "
        cat > /etc/systemd/system/dmserver.service << 'EOF'
$service_content
EOF
        
        systemctl daemon-reload
        systemctl enable dmserver
    "
    
    log_info "达梦数据库服务配置完成: $host"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始达梦数据库集群部署..."
    
    # 获取锁
    if ! acquire_lock "deploy_dameng"; then
        log_error "无法获取锁，可能有其他达梦数据库部署实例正在运行"
        exit 1
    fi
    
    # 检查达梦数据库主机配置
    if [[ ${#DAMENG_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置达梦数据库主机"
        exit 1
    fi
    
    log_info "达梦数据库集群主机: ${DAMENG_HOSTS[*]}"

    # 安装阶段
    if [[ "$SKIP_INSTALL" != "true" ]]; then
        for host in "${DAMENG_HOSTS[@]}"; do
            prepare_dameng_environment "$host"
            install_dameng_database "$host"
        done
    else
        log_warn "跳过达梦数据库安装"
    fi

    # 配置阶段
    if [[ "$SKIP_CONFIG" != "true" ]]; then
        local host_index=0
        for host in "${DAMENG_HOSTS[@]}"; do
            local role="primary"
            if [[ $host_index -gt 0 ]]; then
                role="standby"
            fi

            create_database_instance "$host" "$role"
            configure_dameng_service "$host"
            host_index=$((host_index + 1))
        done
    else
        log_warn "跳过达梦数据库配置"
    fi

    # 启动和初始化阶段
    if [[ "$SKIP_INIT" != "true" ]]; then
        start_dameng_cluster
        configure_replication
        verify_dameng_cluster
    else
        log_warn "跳过达梦数据库集群初始化"
    fi

    log_info "达梦数据库集群部署完成"
}

# =============================================================================
# 集群管理函数
# =============================================================================

start_dameng_cluster() {
    log_info "启动达梦数据库集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动达梦数据库集群"
        return 0
    fi

    # 启动所有数据库实例
    for host in "${DAMENG_HOSTS[@]}"; do
        log_info "启动达梦数据库服务: $host"
        start_service "$host" "dmserver"

        # 等待服务启动
        if ! wait_for_port "$host" "$DAMENG_PORT" 60; then
            log_error "达梦数据库服务启动失败: $host"
            return 1
        fi
    done

    log_info "达梦数据库集群启动完成"
}

configure_replication() {
    log_info "配置达梦数据库主从复制..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将配置达梦数据库主从复制"
        return 0
    fi

    local primary_host="${DAMENG_HOSTS[0]}"

    # 在主库上配置复制
    remote_execute "$primary_host" "
        su - $DM_USER -c '
        $DM_HOME/bin/disql SYSDBA/$(generate_password)@$primary_host:$DAMENG_PORT << EOF
        -- 启用归档模式
        ALTER DATABASE MOUNT;
        ALTER DATABASE ARCHIVELOG;
        ALTER DATABASE OPEN;

        -- 创建复制用户
        CREATE USER repl_user IDENTIFIED BY \"$(generate_password)\";
        GRANT DBA TO repl_user;

        -- 配置主库参数
        ALTER SYSTEM SET ARCH_INI = 1;
        ALTER SYSTEM SET RLOG_SEND_APPLY_MON = 1;

        EXIT;
EOF
        '
    "

    # 配置备库
    local host_index=1
    for host in "${DAMENG_HOSTS[@]:1}"; do
        log_info "配置备库: $host"

        remote_execute "$host" "
            su - $DM_USER -c '
            $DM_HOME/bin/disql SYSDBA/$(generate_password)@$host:$DAMENG_PORT << EOF
            -- 配置备库参数
            ALTER SYSTEM SET ARCH_INI = 1;
            ALTER SYSTEM SET RLOG_APPLY_MON = 1;
            ALTER SYSTEM SET PRIMARY_HOST = \"$primary_host\";
            ALTER SYSTEM SET PRIMARY_PORT = $DAMENG_PORT;

            -- 启动日志应用
            ALTER DATABASE RECOVER MANAGED STANDBY DATABASE;

            EXIT;
EOF
            '
        "
        host_index=$((host_index + 1))
    done

    log_info "达梦数据库主从复制配置完成"
}

verify_dameng_cluster() {
    log_info "验证达梦数据库集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将验证达梦数据库集群"
        return 0
    fi

    local primary_host="${DAMENG_HOSTS[0]}"

    # 验证主库状态
    if remote_execute "$primary_host" "
        su - $DM_USER -c '
        $DM_HOME/bin/disql SYSDBA/$(generate_password)@$primary_host:$DAMENG_PORT << EOF
        SELECT * FROM V\$DATABASE;
        SELECT * FROM V\$INSTANCE;
        EXIT;
EOF
        '
    "; then
        log_info "主库验证成功: $primary_host"
    else
        log_error "主库验证失败: $primary_host"
        return 1
    fi

    # 验证备库状态
    for host in "${DAMENG_HOSTS[@]:1}"; do
        if remote_execute "$host" "
            su - $DM_USER -c '
            $DM_HOME/bin/disql SYSDBA/$(generate_password)@$host:$DAMENG_PORT << EOF
            SELECT * FROM V\$DATABASE;
            SELECT * FROM V\$MANAGED_STANDBY;
            EXIT;
EOF
            '
        "; then
            log_info "备库验证成功: $host"
        else
            log_error "备库验证失败: $host"
            return 1
        fi
    done

    log_info "达梦数据库集群验证完成"
}

# 执行主函数
main "$@"
