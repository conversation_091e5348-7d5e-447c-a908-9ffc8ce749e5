#!/bin/bash
# 通用函数库 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，提供可重用的函数和错误处理机制

# =============================================================================
# 全局变量
# =============================================================================

# 脚本基础路径
SCRIPT_BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CONFIG_DIR="$SCRIPT_BASE_DIR/config"
LIB_DIR="$SCRIPT_BASE_DIR/lib"
TEMPLATES_DIR="$SCRIPT_BASE_DIR/templates"

# 加载配置文件
source "$CONFIG_DIR/global.conf" 2>/dev/null || {
    echo "错误: 无法加载全局配置文件" >&2
    exit 1
}

source "$CONFIG_DIR/hosts.conf" 2>/dev/null || {
    echo "错误: 无法加载主机配置文件" >&2
    exit 1
}

# =============================================================================
# 错误处理和陷阱
# =============================================================================

# 错误处理函数
handle_error() {
    local exit_code=$?
    local line_number=$1
    log_error "脚本在第 $line_number 行发生错误，退出码: $exit_code"
    cleanup_on_exit
    exit $exit_code
}

# 清理函数
cleanup_on_exit() {
    log_info "执行清理操作..."
    
    # 清理临时文件
    if [[ -n "$TEMP_DIR" && -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"
        log_info "清理临时目录: $TEMP_DIR"
    fi
    
    # 清理锁文件
    if [[ -n "$LOCK_FILE" && -f "$LOCK_FILE" ]]; then
        rm -f "$LOCK_FILE"
        log_info "清理锁文件: $LOCK_FILE"
    fi
}

# 设置错误陷阱
trap 'handle_error $LINENO' ERR
trap 'cleanup_on_exit' EXIT

# =============================================================================
# 日志函数
# =============================================================================

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 日志函数
log_info() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [INFO] $*" | tee -a "$LOG_DIR/deploy.log"
}

log_error() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [ERROR] $*" | tee -a "$LOG_DIR/deploy.log" >&2
}

log_warn() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [WARN] $*" | tee -a "$LOG_DIR/deploy.log"
}

log_debug() {
    if [[ "$LOG_LEVEL" == "DEBUG" ]]; then
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        echo "[$timestamp] [DEBUG] $*" | tee -a "$LOG_DIR/deploy.log"
    fi
}

# 进度条函数
show_progress() {
    local current=$1
    local total=$2
    local message=${3:-"处理中"}
    local width=50
    local percentage=$((current * 100 / total))
    local completed=$((current * width / total))
    
    printf "\r%s [" "$message"
    printf "%*s" $completed | tr ' ' '='
    printf "%*s" $((width - completed)) | tr ' ' '-'
    printf "] %d%% (%d/%d)" $percentage $current $total
    
    if [[ $current -eq $total ]]; then
        echo
    fi
}

# =============================================================================
# 系统检查函数
# =============================================================================

# 检查操作系统
check_os() {
    local os_info
    if [[ -f /etc/os-release ]]; then
        os_info=$(grep "^NAME=" /etc/os-release | cut -d'"' -f2)
        log_info "检测到操作系统: $os_info"
        
        if [[ "$os_info" =~ "Kylin" ]]; then
            log_info "银河麒麟操作系统检测通过"
            return 0
        else
            log_warn "当前操作系统不是银河麒麟，可能存在兼容性问题"
            return 0
        fi
    else
        log_error "无法检测操作系统信息"
        return 1
    fi
}

# 检查CPU架构
check_cpu() {
    local cpu_arch=$(uname -m)
    local cpu_info=$(lscpu | grep "Model name" | cut -d':' -f2 | xargs)
    
    log_info "CPU架构: $cpu_arch"
    log_info "CPU信息: $cpu_info"
    
    if [[ "$cpu_arch" == "x86_64" ]]; then
        log_info "CPU架构检测通过"
        
        # 检查是否为海光CPU
        if [[ "$cpu_info" =~ "Hygon" ]]; then
            log_info "检测到海光CPU"
            export IS_HYGON_CPU=true
        else
            log_warn "未检测到海光CPU，请确认兼容性"
            export IS_HYGON_CPU=false
        fi
        
        # 检查AVX支持
        if grep -q avx /proc/cpuinfo; then
            log_warn "CPU支持AVX指令集，编译时需要禁用"
            export HAS_AVX=true
        else
            log_info "CPU不支持AVX指令集"
            export HAS_AVX=false
        fi
        
        return 0
    else
        log_error "不支持的CPU架构: $cpu_arch"
        return 1
    fi
}

# 检查内存
check_memory() {
    local total_mem=$(free -g | awk '/^Mem:/{print $2}')
    local available_mem=$(free -g | awk '/^Mem:/{print $7}')
    
    log_info "总内存: ${total_mem}GB"
    log_info "可用内存: ${available_mem}GB"
    
    if [[ $total_mem -ge 8 ]]; then
        log_info "内存检测通过"
        return 0
    else
        log_error "内存不足，至少需要8GB"
        return 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    local required_space=${1:-50}  # GB
    local mount_point=${2:-"/"}
    
    local available_space=$(df -BG "$mount_point" | awk 'NR==2 {print $4}' | sed 's/G//')
    
    log_info "挂载点 $mount_point 可用空间: ${available_space}GB"
    
    if [[ $available_space -ge $required_space ]]; then
        log_info "磁盘空间检测通过"
        return 0
    else
        log_error "磁盘空间不足，需要至少 ${required_space}GB，当前可用 ${available_space}GB"
        return 1
    fi
}

# 检查网络连通性
check_network_connectivity() {
    local test_hosts=("*******" "***************")
    
    for host in "${test_hosts[@]}"; do
        if ping -c 3 -W 5 "$host" >/dev/null 2>&1; then
            log_info "网络连通性检测通过 ($host)"
            return 0
        fi
    done
    
    log_error "网络连通性检测失败"
    return 1
}

# =============================================================================
# SSH和远程执行函数
# =============================================================================

# 检查SSH连接
check_ssh_connection() {
    local host=$1
    local user=${2:-$DEPLOY_USER}
    local timeout=${3:-$SSH_TIMEOUT}
    
    if ssh -o ConnectTimeout=$timeout -o BatchMode=yes "$user@$host" "echo 'SSH连接测试成功'" >/dev/null 2>&1; then
        log_debug "SSH连接到 $host 成功"
        return 0
    else
        log_error "SSH连接到 $host 失败"
        return 1
    fi
}

# 远程执行命令
remote_execute() {
    local host=$1
    local command=$2
    local user=${3:-$DEPLOY_USER}
    local timeout=${4:-$DEPLOY_TIMEOUT}
    log_info "在 $host 上执行命令: $command"
    ssh -o ConnectTimeout=$timeout -o BatchMode=yes "$user@$host" "$command"
    local exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        log_debug "命令在 $host 上执行成功"
    else
        log_debug "命令在 $host 上执行失败，退出码: $exit_code"
    fi
    
    return $exit_code
}

# 远程复制文件
remote_copy() {
    local source=$1
    local destination=$2
    local host=$3
    local user=${4:-$DEPLOY_USER}
    
    log_debug "复制文件到 $host: $source -> $destination"
    
    scp -o ConnectTimeout=$SSH_TIMEOUT -o BatchMode=yes "$source" "$user@$host:$destination"
    local exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        log_debug "文件复制到 $host 成功"
    else
        log_error "文件复制到 $host 失败，退出码: $exit_code"
    fi
    
    return $exit_code
}

remote_copy_dir() {
    local source=$1
    local destination=$2
    local host=$3
    local user=${4:-$DEPLOY_USER}

    log_debug "复制文件到 $host: $source -> $destination"

    scp -o ConnectTimeout=$SSH_TIMEOUT -o BatchMode=yes "$source"/* "$user@$host:$destination"
    local exit_code=$?

    if [[ $exit_code -eq 0 ]]; then
        log_debug "目录复制到 $host 成功"
    else
        log_error "目录复制到 $host 失败，退出码: $exit_code"
    fi

    return 0
}

# 批量远程执行
batch_remote_execute() {
    local hosts_array=("$@")
    local command="${hosts_array[-1]}"
    unset hosts_array[-1]
    
    local success_count=0
    local total_count=${#hosts_array[@]}
    
    for host in "${hosts_array[@]}"; do
        if remote_execute "$host" "$command"; then
             success_count=$((success_count + 1))
        fi
        show_progress $((success_count + total_count - ${#hosts_array[@]})) $total_count "执行命令"
    done
    
    log_info "批量执行完成: $success_count/$total_count 成功"
    
    if [[ $success_count -eq $total_count ]]; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# 服务管理函数
# =============================================================================

# 检查服务状态
check_service_status() {
    local host=$1
    local service=$2
    
    if remote_execute "$host" "systemctl is-active $service" >/dev/null 2>&1; then
        log_debug "服务 $service 在 $host 上运行正常"
        return 0
    else
        log_debug "服务 $service 在 $host 上未运行"
        return 1
    fi
}

# 启动服务
start_service() {
    local host=$1
    local service=$2
    
    log_info "在 $host 上启动服务 $service"
    
    if remote_execute "$host" "systemctl start $service"; then
        log_info "服务 $service 在 $host 上启动成功"
        return 0
    else
        log_error "服务 $service 在 $host 上启动失败"
        return 1
    fi
}

# 停止服务
stop_service() {
    local host=$1
    local service=$2
    
    log_info "在 $host 上停止服务 $service"
    
    if remote_execute "$host" "systemctl stop $service"; then
        log_info "服务 $service 在 $host 上停止成功"
        return 0
    else
        log_error "服务 $service 在 $host 上停止失败"
        return 1
    fi
}

# 重启服务
restart_service() {
    local host=$1
    local service=$2
    
    log_info "在 $host 上重启服务 $service"
    
    if remote_execute "$host" "systemctl restart $service"; then
        log_info "服务 $service 在 $host 上重启成功"
        return 0
    else
        log_error "服务 $service 在 $host 上重启失败"
        return 1
    fi
}

# =============================================================================
# 包管理函数
# =============================================================================

# 安装系统包
install_system_packages() {
    local host=$1
    shift
    local packages=("$@")

    log_info "在 $host 上安装系统包: ${packages[*]}"

    # 根据部署模式选择安装方式
    if [[ "$OFFLINE_MODE" == "true" ]]; then
        install_packages_offline "$host" "${packages[@]}"
    else
        install_packages_online "$host" "${packages[@]}"
    fi
}

# 在线安装系统包
install_packages_online() {
    local host=$1
    shift
    local packages=("$@")

    log_info "使用在线模式安装系统包"

    local install_cmd="yum install -y ${packages[*]}"

    if remote_execute "$host" "$install_cmd"; then
        log_info "系统包安装成功"
        return 0
    else
        log_error "系统包安装失败"
        return 1
    fi
}

# 离线安装系统包
install_packages_offline() {
    local host=$1
    shift
    local packages=("$@")

    log_info "使用离线模式安装系统包"

    # 检查本地仓库是否配置
    if [[ ! -d "$LOCAL_YUM_REPO" ]]; then
        log_error "离线模式下本地YUM仓库不存在: $LOCAL_YUM_REPO"
        return 1
    fi

    # 配置本地YUM仓库
    remote_execute "$host" "
        # 备份原始仓库配置
        mkdir -p /etc/yum.repos.d/backup
        mv /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/ 2>/dev/null || true

        # 创建本地仓库配置
        cat > /etc/yum.repos.d/local.repo << 'EOF'
[local-base]
name=Local Base Repository
baseurl=file://$LOCAL_YUM_REPO
enabled=1
gpgcheck=0
EOF

        # 清理缓存并重建
        yum clean all
        yum makecache
    "
    local createrepo_cmd="rpm -Uvh $LOCAL_YUM_REPO/createrepo_c-1.0.1-1.oe2403.x86_64.rpm --nodeps --force
                          rpm -Uvh $LOCAL_YUM_REPO/drpm-0.5.2-1.oe2403.x86_64.rpm --nodeps --force
                          createrepo --database $LOCAL_YUM_REPO"
    if remote_execute "$host" "$createrepo_cmd"; then
        log_info "离线createrepo安装成功"
    else
        log_error "离线createrepo安装失败"
        return 1
    fi
    # 安装软件包
    local install_cmd="yum install -y --disablerepo=* --enablerepo=local-* ${packages[*]}"

    if remote_execute "$host" "$install_cmd"; then
        log_info "离线系统包安装成功"
        return 0
    else
        log_error "离线系统包安装失败"
        return 1
    fi
}

# 检查包是否已安装
check_package_installed() {
    local host=$1
    local package=$2
    
    if remote_execute "$host" "rpm -q $package" >/dev/null 2>&1; then
        log_debug "包 $package 在 $host 上已安装"
        return 0
    else
        log_debug "包 $package 在 $host 上未安装"
        return 1
    fi
}

# =============================================================================
# 配置文件处理函数
# =============================================================================

# 从模板生成配置文件
generate_config_from_template() {
    local template_file=$1
    local output_file=$2
    local -A variables=()
    
    # 读取变量参数
    shift 2
    while [[ $# -gt 0 ]]; do
        local key=$1
        local value=$2
        variables["$key"]="$value"
        shift 2
    done
    
    log_debug "从模板生成配置文件: $template_file -> $output_file"
    
    if [[ ! -f "$template_file" ]]; then
        log_error "模板文件不存在: $template_file"
        return 1
    fi
    
    # 复制模板文件
    cp "$template_file" "$output_file"
    
    # 替换变量
    for key in "${!variables[@]}"; do
        local value="${variables[$key]}"
        sed -i "s|{{$key}}|$value|g" "$output_file"
    done
    
    log_debug "配置文件生成完成: $output_file"
    return 0
}

# 备份配置文件
backup_config_file() {
    local host=$1
    local config_file=$2
    local backup_suffix=$(date +%Y%m%d_%H%M%S)
    
    log_info "备份 $host 上的配置文件: $config_file"
    
    if remote_execute "$host" "cp $config_file ${config_file}.backup_${backup_suffix}"; then
        log_info "配置文件备份成功"
        return 0
    else
        log_error "配置文件备份失败"
        return 1
    fi
}

# =============================================================================
# 健康检查函数
# =============================================================================

# 检查端口是否开放
check_port_open() {
    local host=$1
    local port=$2
    local timeout=${3:-5}
    
    if timeout $timeout bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        log_debug "端口 $port 在 $host 上开放"
        return 0
    else
        log_debug "端口 $port 在 $host 上未开放"
        return 1
    fi
}

# 等待端口开放
wait_for_port() {
    local host=$1
    local port=$2
    local timeout=${3:-60}
    local interval=${4:-5}
    
    log_info "等待 $host:$port 端口开放..."
    
    local elapsed=0
    while [[ $elapsed -lt $timeout ]]; do
        if check_port_open "$host" "$port"; then
            log_info "端口 $host:$port 已开放"
            return 0
        fi
        
        sleep $interval
        elapsed=$((elapsed + interval))
        log_debug "等待端口开放... ($elapsed/$timeout 秒)"
    done
    
    log_error "等待端口 $host:$port 开放超时"
    return 1
}

# 检查服务健康状态
check_service_health() {
    local host=$1
    local service=$2
    local health_check_cmd=$3
    
    log_debug "检查 $host 上服务 $service 的健康状态"
    
    if remote_execute "$host" "$health_check_cmd"; then
        log_debug "服务 $service 在 $host 上健康"
        return 0
    else
        log_debug "服务 $service 在 $host 上不健康"
        return 1
    fi
}

# =============================================================================
# 锁机制
# =============================================================================

# 获取锁
acquire_lock() {
    local lock_name=$1
    local timeout=${2:-300}
    
    LOCK_FILE="/tmp/${lock_name}.lock"
    
    local elapsed=0
    while [[ $elapsed -lt $timeout ]]; do
        if (set -C; echo $$ > "$LOCK_FILE") 2>/dev/null; then
            log_info "获取锁成功: $lock_name"
            return 0
        fi
        
        sleep 1
        elapsed=$((elapsed + 1))
    done
    
    log_error "获取锁超时: $lock_name"
    return 1
}

# 释放锁
release_lock() {
    if [[ -n "$LOCK_FILE" && -f "$LOCK_FILE" ]]; then
        rm -f "$LOCK_FILE"
        log_info "释放锁: $LOCK_FILE"
        LOCK_FILE=""
    fi
}

# =============================================================================
# 初始化
# =============================================================================

# 创建临时目录
TEMP_DIR=$(mktemp -d)
export TEMP_DIR

# =============================================================================
# 离线安装支持函数
# =============================================================================

# 检查离线安装环境
check_offline_environment() {
    local host=$1

    log_info "检查 $host 的离线安装环境..."

    if [[ "$OFFLINE_MODE" != "true" ]]; then
        log_debug "当前为在线模式，跳过离线环境检查"
        return 0
    fi

    local errors=0

    # 检查本地软件仓库
    if [[ ! -d "$SOFTWARE_REPO" ]]; then
        log_error "软件仓库目录不存在: $SOFTWARE_REPO"
        errors=$((errors + 1))
    fi

    # 检查本地YUM仓库
    if [[ "$USE_LOCAL_REPO" == "true" && ! -d "$LOCAL_YUM_REPO" ]]; then
        log_error "本地YUM仓库不存在: $LOCAL_YUM_REPO"
        errors=$((errors + 1))
    fi

    # 检查Python包仓库
    if [[ ! -d "$LOCAL_PIP_REPO" ]]; then
        log_warn "本地PIP仓库不存在: $LOCAL_PIP_REPO"
    fi

    # 检查离线安装包目录
    if [[ ! -d "$OFFLINE_PACKAGE_DIR" ]]; then
        log_warn "离线安装包目录不存在: $OFFLINE_PACKAGE_DIR"
    fi

    if [[ $errors -gt 0 ]]; then
        log_error "离线环境检查失败，请确保所有必需的离线资源已准备"
        return 1
    fi

    log_info "离线环境检查通过"
    return 0
}

# 安装Python包（支持离线）
install_python_packages() {
    local host=$1
    shift
    local packages=("$@")

    log_info "在 $host 上安装Python包: ${packages[*]}"

    if [[ "$OFFLINE_MODE" == "true" ]]; then
        install_python_packages_offline "$host" "${packages[@]}"
    else
        install_python_packages_online "$host" "${packages[@]}"
    fi
}

# 在线安装Python包
install_python_packages_online() {
    local host=$1
    shift
    local packages=("$@")

    local install_cmd="pip3 install ${packages[*]}"

    if remote_execute "$host" "$install_cmd"; then
        log_info "Python包在线安装成功"
        return 0
    else
        log_error "Python包在线安装失败"
        return 1
    fi
}

# 离线安装Python包
install_python_packages_offline() {
    local host=$1
    shift
    local packages=("$@")

    if [[ ! -d "$LOCAL_PIP_REPO" ]]; then
        log_error "离线模式下本地PIP仓库不存在: $LOCAL_PIP_REPO"
        return 1
    fi

    # 配置pip使用本地仓库
    remote_execute "$host" "
        mkdir -p ~/.pip
        cat > ~/.pip/pip.conf << 'EOF'
[global]
index-url = file://$LOCAL_PIP_REPO/simple
trusted-host = localhost
EOF
    "

    local install_cmd="pip3 install --no-index --find-links $LOCAL_PIP_REPO ${packages[*]}"

    if remote_execute "$host" "$install_cmd"; then
        log_info "Python包离线安装成功"
        return 0
    else
        log_error "Python包离线安装失败"
        return 1
    fi
}

# 下载文件（支持离线）
download_file() {
    local url=$1
    local dest_file=$2
    local host=${3:-"localhost"}

    if [[ "$OFFLINE_MODE" == "true" ]]; then
        log_warn "离线模式下跳过文件下载: $url"
        return 0
    fi

    log_info "下载文件: $url -> $dest_file"

    if [[ "$host" == "localhost" ]]; then
        wget -O "$dest_file" "$url" || curl -o "$dest_file" "$url"
    else
        remote_execute "$host" "wget -O '$dest_file' '$url' || curl -o '$dest_file' '$url'"
    fi
}

# 检查网络连通性（适配离线模式）
check_network_connectivity_adaptive() {
    if [[ "$OFFLINE_MODE" == "true" ]]; then
        log_info "离线模式下跳过外网连通性检查"
        return 0
    fi

    check_network_connectivity
}

# 配置离线YUM仓库
setup_offline_yum_repo() {
    local host=$1

    if [[ "$OFFLINE_MODE" != "true" ]]; then
        return 0
    fi

    log_info "在 $host 上配置离线YUM仓库"

    remote_execute "$host" "
        # 备份原始仓库配置
        mkdir -p /etc/yum.repos.d/backup
        mv /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/ 2>/dev/null || true

        # 创建本地仓库配置
        cat > /etc/yum.repos.d/local.repo << 'EOF'
[local-base]
name=Local Base Repository
baseurl=file://$LOCAL_YUM_REPO
enabled=1
gpgcheck=0
EOF

        # 清理缓存并重建
        yum clean all
    "
    local createrepo_cmd="rpm -Uvh $LOCAL_YUM_REPO/createrepo_c-1.0.1-1.oe2403.x86_64.rpm --nodeps --force
                          rpm -Uvh $LOCAL_YUM_REPO/drpm-0.5.2-1.oe2403.x86_64.rpm --nodeps --force
                          createrepo --database $LOCAL_YUM_REPO
                          yum makecache"
    if remote_execute "$host" "$createrepo_cmd"; then
        log_info "离线createrepo安装成功"
        return 0
    else
        log_error "离线createrepo安装失败"
        return 1
    fi
    # 安装软件包
    local install_cmd="yum install -y --disablerepo=* --enablerepo=local-* ${packages[*]}"

    if remote_execute "$host" "$install_cmd"; then
        log_info "离线系统包安装成功"
        return 0
    else
        log_error "离线系统包安装失败"
        return 1
    fi
}

log_debug "通用函数库加载完成"
log_debug "临时目录: $TEMP_DIR"
log_debug "部署模式: $([ "$OFFLINE_MODE" == "true" ] && echo "离线" || echo "在线")"
