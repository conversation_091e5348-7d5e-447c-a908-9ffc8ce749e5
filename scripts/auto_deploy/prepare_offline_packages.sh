#!/bin/bash
# 离线安装包准备脚本 - 工业场景基础服务自动部署
# 用于在有网络的环境中准备离线安装所需的所有软件包

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="离线安装包准备脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 离线包配置
# =============================================================================

# 离线包目录
OFFLINE_PREP_DIR="/apps/offline-prep"
OFFLINE_YUM_DIR="$OFFLINE_PREP_DIR/yum-repo"
OFFLINE_PIP_DIR="$OFFLINE_PREP_DIR/pip-repo"
OFFLINE_SOFTWARE_DIR="$OFFLINE_PREP_DIR/software"

# 系统包列表
SYSTEM_PACKAGES=(
    # 基础工具
    "wget" "curl" "vim" "git" "htop" "iotop" "iftop" "nload"
    "net-tools" "telnet" "nc" "tcpdump" "wireshark-cli"
    "rsync" "screen" "tmux" "tree" "lsof" "strace"
    
    # 编译工具
    "gcc" "gcc-c++" "make" "cmake" "autoconf" "automake"
    "openssl-devel" "zlib-devel" "pcre-devel" "libtool"
    
    # Python环境
    "python3" "python3-pip" "python3-devel"
    
    # Java环境
    "java-11-openjdk" "java-11-openjdk-devel"
    
    # 监控工具
    "sysstat" "dstat" "iostat" "vmstat" "mpstat"
    "perf" "numactl" "numactl-devel"
    
    # 数据库相关
    "libaio-devel" "readline-devel" "ncurses-devel"
    "libcurl-devel" "liblzma-devel"
    
    # 网络工具
    "chrony" "firewalld"
    
    # 其他依赖
    "tcl-devel" "systemd-devel" "glibc-devel" "glibc-static"
    "libstdc++-static" "libstdc++-devel"
)

# Python包列表
PYTHON_PACKAGES=(
    "scons==4.4.0"
    "pymongo"
    "redis"
    "kafka-python"
    "prometheus-client"
    "psutil"
    "requests"
    "pyyaml"
    "jinja2"
)

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_YUM=false
SKIP_PIP=false
SKIP_SOFTWARE=false
FORCE_DOWNLOAD=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-yum)
            SKIP_YUM=true
            shift
            ;;
        --skip-pip)
            SKIP_PIP=true
            shift
            ;;
        --skip-software)
            SKIP_SOFTWARE=true
            shift
            ;;
        --force-download)
            FORCE_DOWNLOAD=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-yum          跳过YUM包下载"
            echo "  --skip-pip          跳过Python包下载"
            echo "  --skip-software     跳过软件包下载"
            echo "  --force-download    强制重新下载"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# 离线包准备函数
# =============================================================================

prepare_directories() {
    log_info "准备离线包目录结构..."
    
    mkdir -p "$OFFLINE_PREP_DIR"
    mkdir -p "$OFFLINE_YUM_DIR"/{base,epel,extras,updates}
    mkdir -p "$OFFLINE_PIP_DIR"
    mkdir -p "$OFFLINE_SOFTWARE_DIR"/{mongodb,redis,kafka,tdengine,nebula,dameng,monitoring,jdk}
    
    log_info "目录结构创建完成"
}

download_yum_packages() {
    if [[ "$SKIP_YUM" == "true" ]]; then
        log_warn "跳过YUM包下载"
        return 0
    fi
    
    log_info "下载YUM软件包..."
    
    # 安装必要工具
    yum install -y yum-utils createrepo
    
    # 下载系统包及其依赖
    log_info "下载系统包及依赖..."
    for package in "${SYSTEM_PACKAGES[@]}"; do
        log_info "下载包: $package"
        yumdownloader --resolve --destdir="$OFFLINE_YUM_DIR/base" "$package" || log_warn "下载失败: $package"
    done
    
    # 下载EPEL包
    log_info "下载EPEL包..."
    yum install -y epel-release
    yumdownloader --resolve --destdir="$OFFLINE_YUM_DIR/epel" --enablerepo=epel \
        htop iotop iftop nload || log_warn "EPEL包下载失败"
    
    # 创建本地仓库
    log_info "创建本地YUM仓库..."
    createrepo "$OFFLINE_YUM_DIR/base"
    createrepo "$OFFLINE_YUM_DIR/epel"
    createrepo "$OFFLINE_YUM_DIR/extras"
    
    log_info "YUM包下载完成"
}

download_python_packages() {
    if [[ "$SKIP_PIP" == "true" ]]; then
        log_warn "跳过Python包下载"
        return 0
    fi
    
    log_info "下载Python软件包..."
    
    # 升级pip
    pip3 install --upgrade pip
    
    # 下载Python包
    for package in "${PYTHON_PACKAGES[@]}"; do
        log_info "下载Python包: $package"
        pip3 download --dest "$OFFLINE_PIP_DIR" "$package" || log_warn "下载失败: $package"
    done
    
    # 创建简单索引
    cd "$OFFLINE_PIP_DIR"
    mkdir -p simple
    for pkg in *.whl *.tar.gz; do
        if [[ -f "$pkg" ]]; then
            pkg_name=$(echo "$pkg" | sed 's/-[0-9].*//' | tr '[:upper:]' '[:lower:]')
            mkdir -p "simple/$pkg_name"
            ln -sf "../../$pkg" "simple/$pkg_name/"
        fi
    done
    
    log_info "Python包下载完成"
}

download_software_packages() {
    if [[ "$SKIP_SOFTWARE" == "true" ]]; then
        log_warn "跳过软件包下载"
        return 0
    fi
    
    log_info "下载软件安装包..."
    
    # 创建下载说明文件
    cat > "$OFFLINE_SOFTWARE_DIR/README.md" << 'EOF'
# 离线软件包说明

请手动下载以下软件包到对应目录：

## MongoDB (mongodb/)
- mongodb-src-r4.4.18.tar.gz (源码包)
- 或 mongodb-hygon-precompiled.tar.gz (海光CPU预编译包)

## Redis (redis/)
- redis-7.0.8.tar.gz (源码包)
- 或 redis-hygon-precompiled.tar.gz (海光CPU预编译包)

## Kafka (kafka/)
- kafka_2.13-3.4.0.tgz (Kafka二进制包)
- 或 apache-kafka-3.4.0.tar.gz

## TDEngine (tdengine/)
- TDengine-server-*******.tar.gz
- 或 TDengine-server-*******.rpm

## NebulaGraph (nebula/)
- nebula-graph-3.5.0.tar.gz

## 达梦数据库 (dameng/)
- dm8_20230808_x86_rh6_64.iso
- 或 dm8_20230808_x86_rh6_64.tar.gz

## 监控系统 (monitoring/)
- prometheus-2.45.0.linux-amd64.tar.gz
- grafana-10.0.0.linux-amd64.tar.gz
- alertmanager-0.25.0.linux-amd64.tar.gz

## JDK (jdk/)
- bisheng-jdk-11.0.19-linux-x64.tar.gz (华为毕昇JDK，推荐)
- 或 openjdk-11.0.19_linux-x64_bin.tar.gz

## 下载地址参考
- MongoDB: https://github.com/mongodb/mongo
- Redis: https://redis.io/download
- Kafka: https://kafka.apache.org/downloads
- TDEngine: https://www.taosdata.com/cn/getting-started
- NebulaGraph: https://github.com/vesoft-inc/nebula
- 达梦数据库: https://www.dameng.com/
- Prometheus: https://prometheus.io/download/
- Grafana: https://grafana.com/grafana/download
- 华为毕昇JDK: https://www.hikunpeng.com/developer/devkit/compiler/jdk
EOF
    
    log_info "软件包下载说明已生成: $OFFLINE_SOFTWARE_DIR/README.md"
}

create_offline_package() {
    log_info "创建离线安装包..."
    
    cd "$(dirname "$OFFLINE_PREP_DIR")"
    
    # 创建压缩包
    local package_name="industrial-infrastructure-offline-$(date +%Y%m%d).tar.gz"
    
    tar -czf "$package_name" "$(basename "$OFFLINE_PREP_DIR")"
    
    log_info "离线安装包已创建: $(pwd)/$package_name"
    
    # 生成安装说明
    cat > "offline-install-guide.md" << 'EOF'
# 离线安装包使用说明

## 1. 解压离线包
```bash
tar -xzf industrial-infrastructure-offline-*.tar.gz
```

## 2. 配置离线模式
编辑 `scripts/auto_deploy/config/global.conf`：
```bash
export OFFLINE_MODE="true"
export INTERNET_ACCESS="false"
export USE_LOCAL_REPO="true"
export LOCAL_YUM_REPO="/apps/offline-prep/yum-repo"
export LOCAL_PIP_REPO="/apps/offline-prep/pip-repo"
export SOFTWARE_REPO="/apps/offline-prep/software"
```

## 3. 分发到目标服务器
```bash
# 复制离线包到所有目标服务器
for host in server1 server2 server3; do
    scp -r offline-prep/ $host:/apps/
done
```

## 4. 执行离线部署
```bash
./deploy_all.sh
```

## 注意事项
- 确保所有软件包已下载到对应目录
- 离线模式下无法进行系统更新
- 时间同步将使用本地时钟
- 建议在部署前验证所有软件包完整性
EOF
    
    log_info "离线安装说明已生成: $(pwd)/offline-install-guide.md"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始准备离线安装包..."
    
    # 检查网络连接
    if ! ping -c 3 ******* >/dev/null 2>&1; then
        log_error "无法连接到互联网，请在有网络的环境中运行此脚本"
        exit 1
    fi
    
    # 准备目录
    prepare_directories
    
    # 下载各类软件包
    download_yum_packages
    download_python_packages
    download_software_packages
    
    # 创建离线安装包
    create_offline_package
    
    log_info "离线安装包准备完成！"
    log_info "请按照 offline-install-guide.md 中的说明进行离线部署"
}

# 执行主函数
main "$@"
