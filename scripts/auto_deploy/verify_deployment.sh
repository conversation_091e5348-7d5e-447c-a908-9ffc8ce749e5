#!/bin/bash
# 部署验证脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化部署结果验证

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="部署验证脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 全局变量
# =============================================================================

# 验证结果跟踪
declare -A VERIFICATION_RESULTS=()

# 验证统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
VERIFY_INFRASTRUCTURE=true
VERIFY_DATABASES=true
VERIFY_CACHE=true
VERIFY_MESSAGING=true
VERIFY_MONITORING=true
DETAILED_OUTPUT=false
GENERATE_REPORT=true

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-infrastructure)
            VERIFY_INFRASTRUCTURE=false
            shift
            ;;
        --skip-databases)
            VERIFY_DATABASES=false
            shift
            ;;
        --skip-cache)
            VERIFY_CACHE=false
            shift
            ;;
        --skip-messaging)
            VERIFY_MESSAGING=false
            shift
            ;;
        --skip-monitoring)
            VERIFY_MONITORING=false
            shift
            ;;
        --detailed)
            DETAILED_OUTPUT=true
            shift
            ;;
        --no-report)
            GENERATE_REPORT=false
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-infrastructure   跳过基础设施验证"
            echo "  --skip-databases        跳过数据库验证"
            echo "  --skip-cache            跳过缓存服务验证"
            echo "  --skip-messaging        跳过消息队列验证"
            echo "  --skip-monitoring       跳过监控系统验证"
            echo "  --detailed              显示详细输出"
            echo "  --no-report             不生成验证报告"
            echo "  -h, --help              显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# 验证辅助函数
# =============================================================================

# 记录验证结果
record_check_result() {
    local check_name=$1
    local result=$2
    local message=${3:-""}
    
    VERIFICATION_RESULTS["$check_name"]="$result:$message"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    case $result in
        "PASS")
             PASSED_CHECKS=$((PASSED_CHECKS + 1))
            if [[ "$DETAILED_OUTPUT" == "true" ]]; then
                log_info "✓ $check_name: $message"
            fi
            ;;
        "FAIL")
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            log_error "✗ $check_name: $message"
            ;;
        "WARN")
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            log_warn "⚠ $check_name: $message"
            ;;
    esac
}

# 检查服务状态
check_service_running() {
    local host=$1
    local service=$2
    local check_name="$service@$host"
    
    if check_service_status "$host" "$service"; then
        record_check_result "$check_name" "PASS" "服务运行正常"
        return 0
    else
        record_check_result "$check_name" "FAIL" "服务未运行"
        return 1
    fi
}

# 检查端口连通性
check_port_connectivity() {
    local host=$1
    local port=$2
    local service_name=${3:-"$host:$port"}
    local check_name="Port_$service_name"
    
    if check_port_open "$host" "$port"; then
        record_check_result "$check_name" "PASS" "端口 $port 可访问"
        return 0
    else
        record_check_result "$check_name" "FAIL" "端口 $port 不可访问"
        return 1
    fi
}

# 检查进程是否运行
check_process_running() {
    local host=$1
    local process_name=$2
    local check_name="Process_${process_name}@$host"
    
    if remote_execute "$host" "pgrep -f '$process_name'" >/dev/null 2>&1; then
        record_check_result "$check_name" "PASS" "进程 $process_name 运行正常"
        return 0
    else
        record_check_result "$check_name" "FAIL" "进程 $process_name 未运行"
        return 1
    fi
}

# =============================================================================
# 基础设施验证函数
# =============================================================================

verify_infrastructure() {
    if [[ "$VERIFY_INFRASTRUCTURE" != "true" ]]; then
        return 0
    fi
    
    log_info "验证基础设施..."
    
    # 获取所有主机
    local all_hosts
    mapfile -t all_hosts < <(get_all_hosts)
    
    # 检查SSH连通性
    for host in "${all_hosts[@]}"; do
        local check_name="SSH_$host"
        if check_ssh_connection "$host"; then
            record_check_result "$check_name" "PASS" "SSH连接正常"
        else
            record_check_result "$check_name" "FAIL" "SSH连接失败"
        fi
    done
    
    # 检查系统资源
    for host in "${all_hosts[@]}"; do
        # 检查磁盘空间
        local disk_usage=$(remote_execute "$host" "df -h / | tail -1 | awk '{print \$5}' | sed 's/%//'" 2>/dev/null || echo "100")
        local check_name="DiskSpace_$host"
        
        if [[ $disk_usage -lt 80 ]]; then
            record_check_result "$check_name" "PASS" "磁盘使用率: ${disk_usage}%"
        elif [[ $disk_usage -lt 90 ]]; then
            record_check_result "$check_name" "WARN" "磁盘使用率较高: ${disk_usage}%"
        else
            record_check_result "$check_name" "FAIL" "磁盘空间不足: ${disk_usage}%"
        fi
        
        # 检查内存使用
        local memory_usage=$(remote_execute "$host" "free | grep Mem | awk '{printf \"%.0f\", \$3/\$2 * 100}'" 2>/dev/null || echo "100")
        local check_name="Memory_$host"
        
        if [[ $memory_usage -lt 80 ]]; then
            record_check_result "$check_name" "PASS" "内存使用率: ${memory_usage}%"
        elif [[ $memory_usage -lt 90 ]]; then
            record_check_result "$check_name" "WARN" "内存使用率较高: ${memory_usage}%"
        else
            record_check_result "$check_name" "FAIL" "内存使用率过高: ${memory_usage}%"
        fi
    done
    
    log_info "基础设施验证完成"
}

# =============================================================================
# 数据库验证函数
# =============================================================================

verify_mongodb() {
    log_info "验证MongoDB集群..."
    
    # 检查MongoDB服务
    for host in "${MONGODB_HOSTS[@]}"; do
        check_service_running "$host" "mongod"
        check_port_connectivity "$host" "$MONGODB_PORT" "MongoDB"
        check_process_running "$host" "mongod"
    done
    
    # 检查副本集状态
    local primary_host="${MONGODB_HOSTS[0]}"
    local check_name="MongoDB_ReplicaSet"
    
    if remote_execute "$primary_host" "/apps/mongodb/bin/mongo --host $primary_host:$MONGODB_PORT --eval 'rs.status()'" >/dev/null 2>&1; then
        # 检查副本集成员数量
        local member_count=$(remote_execute "$primary_host" "/apps/mongodb/bin/mongo --host $primary_host:$MONGODB_PORT --eval 'rs.status().members.length' --quiet" 2>/dev/null || echo "0")
        
        if [[ $member_count -eq ${#MONGODB_HOSTS[@]} ]]; then
            record_check_result "$check_name" "PASS" "副本集包含 $member_count 个成员"
        else
            record_check_result "$check_name" "FAIL" "副本集成员数量不正确: $member_count/${#MONGODB_HOSTS[@]}"
        fi
        
        # 检查主节点
        local primary_count=$(remote_execute "$primary_host" "/apps/mongodb/bin/mongo --host $primary_host:$MONGODB_PORT --eval 'rs.status().members.filter(m => m.state === 1).length' --quiet" 2>/dev/null || echo "0")
        local check_name="MongoDB_Primary"
        
        if [[ $primary_count -eq 1 ]]; then
            record_check_result "$check_name" "PASS" "有且仅有1个主节点"
        else
            record_check_result "$check_name" "FAIL" "主节点数量异常: $primary_count"
        fi
    else
        record_check_result "$check_name" "FAIL" "无法获取副本集状态"
    fi
    
    log_info "MongoDB验证完成"
}

verify_dameng() {
    log_info "验证达梦数据库集群..."
    
    # 检查达梦数据库服务
    for host in "${DAMENG_HOSTS[@]}"; do
        check_service_running "$host" "dmserver"
        check_port_connectivity "$host" "$DAMENG_PORT" "DamengDB"
        check_process_running "$host" "dmserver"
    done
    
    # 检查数据库连接
    local primary_host="${DAMENG_HOSTS[0]}"
    local check_name="DamengDB_Connection"
    
    if remote_execute "$primary_host" "/apps/dmdbms/bin/disql SYSDBA/SYSDBA@localhost:$DAMENG_PORT -e 'SELECT 1 FROM DUAL;'" >/dev/null 2>&1; then
        record_check_result "$check_name" "PASS" "数据库连接正常"
    else
        record_check_result "$check_name" "FAIL" "数据库连接失败"
    fi
    
    log_info "达梦数据库验证完成"
}

verify_databases() {
    if [[ "$VERIFY_DATABASES" != "true" ]]; then
        return 0
    fi
    
    log_info "验证数据库服务..."
    
    # 验证MongoDB
    if [[ ${#MONGODB_HOSTS[@]} -gt 0 ]]; then
        verify_mongodb
    fi
    
    # 验证达梦数据库
    if [[ ${#DAMENG_HOSTS[@]} -gt 0 ]]; then
        verify_dameng
    fi
    
    log_info "数据库服务验证完成"
}

# =============================================================================
# 缓存服务验证函数
# =============================================================================

verify_redis() {
    log_info "验证Redis集群..."
    
    # 检查Redis服务
    for host in "${REDIS_HOSTS[@]}"; do
        local port="${REDIS_PORTS[$host]}"
        check_port_connectivity "$host" "$port" "Redis"
        check_process_running "$host" "redis-server.*:$port"
    done
    
    # 检查Redis集群状态
    local first_host="${REDIS_HOSTS[0]}"
    local first_port="${REDIS_PORTS[$first_host]}"
    local check_name="Redis_Cluster"
    
    if remote_execute "$first_host" "/apps/redis/bin/redis-cli -h $first_host -p $first_port cluster info" >/dev/null 2>&1; then
        local cluster_state=$(remote_execute "$first_host" "/apps/redis/bin/redis-cli -h $first_host -p $first_port cluster info | grep cluster_state" 2>/dev/null || echo "cluster_state:fail")
        
        if [[ "$cluster_state" == *"ok"* ]]; then
            record_check_result "$check_name" "PASS" "Redis集群状态正常"
        else
            record_check_result "$check_name" "FAIL" "Redis集群状态异常: $cluster_state"
        fi
        
        # 检查集群节点数量
        local node_count=$(remote_execute "$first_host" "/apps/redis/bin/redis-cli -h $first_host -p $first_port cluster nodes | wc -l" 2>/dev/null || echo "0")
        local check_name="Redis_Nodes"
        
        if [[ $node_count -eq ${#REDIS_HOSTS[@]} ]]; then
            record_check_result "$check_name" "PASS" "Redis集群包含 $node_count 个节点"
        else
            record_check_result "$check_name" "FAIL" "Redis集群节点数量不正确: $node_count/${#REDIS_HOSTS[@]}"
        fi
    else
        record_check_result "$check_name" "FAIL" "无法获取Redis集群信息"
    fi
    
    log_info "Redis验证完成"
}

verify_cache_services() {
    if [[ "$VERIFY_CACHE" != "true" ]]; then
        return 0
    fi
    
    log_info "验证缓存服务..."
    
    # 验证Redis
    if [[ ${#REDIS_HOSTS[@]} -gt 0 ]]; then
        verify_redis
    fi
    
    log_info "缓存服务验证完成"
}

# =============================================================================
# 消息队列验证函数
# =============================================================================

verify_kafka() {
    log_info "验证Kafka集群..."
    
    # 检查Kafka服务
    for host in "${KAFKA_HOSTS[@]}"; do
        check_service_running "$host" "kafka"
        check_port_connectivity "$host" "$KAFKA_PORT" "Kafka"
        check_process_running "$host" "kafka"
    done
    
    # 检查Zookeeper服务
    for host in "${KAFKA_HOSTS[@]}"; do
        check_port_connectivity "$host" "$ZOOKEEPER_PORT" "Zookeeper"
        check_process_running "$host" "zookeeper"
    done
    
    # 检查Kafka集群状态
    local first_host="${KAFKA_HOSTS[0]}"
    local check_name="Kafka_Brokers"
    
    if remote_execute "$first_host" "/apps/kafka/bin/kafka-broker-api-versions.sh --bootstrap-server $first_host:$KAFKA_PORT" >/dev/null 2>&1; then
        record_check_result "$check_name" "PASS" "Kafka集群连接正常"
    else
        record_check_result "$check_name" "FAIL" "Kafka集群连接失败"
    fi
    
    log_info "Kafka验证完成"
}

verify_messaging_services() {
    if [[ "$VERIFY_MESSAGING" != "true" ]]; then
        return 0
    fi
    
    log_info "验证消息队列服务..."
    
    # 验证Kafka
    if [[ ${#KAFKA_HOSTS[@]} -gt 0 ]]; then
        verify_kafka
    fi
    
    log_info "消息队列服务验证完成"
}

# =============================================================================
# 监控系统验证函数
# =============================================================================

verify_monitoring() {
    if [[ "$VERIFY_MONITORING" != "true" ]]; then
        return 0
    fi
    
    log_info "验证监控系统..."
    
    # 检查Prometheus
    for host in "${MONITOR_HOSTS[@]}"; do
        if [[ "${MONITOR_SERVICES[$host]}" == *"prometheus"* ]]; then
            check_service_running "$host" "prometheus"
            check_port_connectivity "$host" "$PROMETHEUS_PORT" "Prometheus"
            check_process_running "$host" "prometheus"
        fi
    done
    
    # 检查Grafana
    for host in "${MONITOR_HOSTS[@]}"; do
        if [[ "${MONITOR_SERVICES[$host]}" == *"grafana"* ]]; then
            check_service_running "$host" "grafana"
            check_port_connectivity "$host" "$GRAFANA_PORT" "Grafana"
            check_process_running "$host" "grafana"
        fi
    done
    
    # 检查AlertManager
    for host in "${MONITOR_HOSTS[@]}"; do
        if [[ "${MONITOR_SERVICES[$host]}" == *"alertmanager"* ]]; then
            check_service_running "$host" "alertmanager"
            check_port_connectivity "$host" "$ALERTMANAGER_PORT" "AlertManager"
            check_process_running "$host" "alertmanager"
        fi
    done
    
    log_info "监控系统验证完成"
}

# =============================================================================
# 报告生成函数
# =============================================================================

generate_verification_report() {
    if [[ "$GENERATE_REPORT" != "true" ]]; then
        return 0
    fi
    
    local report_file="$LOG_DIR/verification_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log_info "生成验证报告: $report_file"
    
    cat > "$report_file" << EOF
# 工业场景基础服务部署验证报告

## 验证摘要
- 验证时间: $(date '+%Y-%m-%d %H:%M:%S')
- 总检查项: $TOTAL_CHECKS
- 通过: $PASSED_CHECKS
- 失败: $FAILED_CHECKS
- 警告: $WARNING_CHECKS
- 成功率: $(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))%

## 详细结果

EOF
    
    # 按类别组织结果
    local categories=("SSH" "DiskSpace" "Memory" "Port" "Process" "MongoDB" "Redis" "Kafka" "DamengDB")
    
    for category in "${categories[@]}"; do
        local has_items=false
        
        # 检查是否有该类别的项目
        for check_name in "${!VERIFICATION_RESULTS[@]}"; do
            if [[ "$check_name" == *"$category"* ]]; then
                if [[ "$has_items" == "false" ]]; then
                    echo "### $category 检查" >> "$report_file"
                    has_items=true
                fi
                
                local result="${VERIFICATION_RESULTS[$check_name]}"
                local status="${result%%:*}"
                local message="${result#*:}"
                
                local status_icon
                case $status in
                    "PASS") status_icon="✓" ;;
                    "FAIL") status_icon="✗" ;;
                    "WARN") status_icon="⚠" ;;
                    *) status_icon="?" ;;
                esac
                
                echo "- $status_icon $check_name: $message" >> "$report_file"
            fi
        done
        
        if [[ "$has_items" == "true" ]]; then
            echo >> "$report_file"
        fi
    done
    
    # 添加建议
    cat >> "$report_file" << EOF
## 建议

EOF
    
    if [[ $FAILED_CHECKS -gt 0 ]]; then
        echo "- 发现 $FAILED_CHECKS 个失败项，请检查相关服务配置和状态" >> "$report_file"
    fi
    
    if [[ $WARNING_CHECKS -gt 0 ]]; then
        echo "- 发现 $WARNING_CHECKS 个警告项，建议关注资源使用情况" >> "$report_file"
    fi
    
    if [[ $FAILED_CHECKS -eq 0 && $WARNING_CHECKS -eq 0 ]]; then
        echo "- 所有检查项均通过，系统运行正常" >> "$report_file"
    fi
    
    log_info "验证报告生成完成: $report_file"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始部署验证..."
    
    # 获取锁
    if ! acquire_lock "verify_deployment"; then
        log_error "无法获取锁，可能有其他验证实例正在运行"
        exit 1
    fi
    
    # 执行验证
    verify_infrastructure
    verify_databases
    verify_cache_services
    verify_messaging_services
    verify_monitoring
    
    # 输出验证摘要
    echo
    echo "=== 验证结果摘要 ==="
    echo "总检查项: $TOTAL_CHECKS"
    echo "通过: $PASSED_CHECKS"
    echo "失败: $FAILED_CHECKS"
    echo "警告: $WARNING_CHECKS"
    
    if [[ $TOTAL_CHECKS -gt 0 ]]; then
        local success_rate=$(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))
        echo "成功率: ${success_rate}%"
    fi
    echo "===================="
    
    # 生成报告
    generate_verification_report
    
    # 返回结果
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        log_info "部署验证完成，所有关键检查项均通过"
        exit 0
    else
        log_error "部署验证发现问题，请检查失败的项目"
        exit 1
    fi
}

# 设置脚本权限
chmod +x "$0"

# 执行主函数
main "$@"
