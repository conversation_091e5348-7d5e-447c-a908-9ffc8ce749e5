#!/bin/bash
# TDEngine集群部署脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化TDEngine集群部署

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="TDEngine集群部署脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# TDEngine特定配置
# =============================================================================

# TDEngine配置
TDENGINE_USER="taos"
TDENGINE_GROUP="taos"
TDENGINE_HOME="/apps/taos"
TDENGINE_DATA_DIR="/apps/data/taos"
TDENGINE_LOG_DIR="/var/log/taos"
TDENGINE_CONFIG_DIR="/etc/taos"

# 安装配置
TDENGINE_SOURCE_DIR="/apps/software/tdengine"

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
SKIP_INSTALL=false
SKIP_CONFIG=false
SKIP_INIT=false
FORCE_REINSTALL=false
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        --skip-config)
            SKIP_CONFIG=true
            shift
            ;;
        --skip-init)
            SKIP_INIT=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-install      跳过安装"
            echo "  --skip-config       跳过配置生成"
            echo "  --skip-init         跳过集群初始化"
            echo "  --force-reinstall   强制重新安装"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

# =============================================================================
# TDEngine安装函数
# =============================================================================

prepare_tdengine_environment() {
    local host=$1
    
    log_info "在 $host 上准备TDEngine环境..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上准备TDEngine环境"
        return 0
    fi

    check_offline_environment "$host"

    # 配置离线YUM仓库（如果是离线模式）
    setup_offline_yum_repo "$host"

    # 创建TDEngine用户
    remote_execute "$host" "
        if ! id $TDENGINE_USER >/dev/null 2>&1; then
            groupadd -g 1005 $TDENGINE_GROUP
            useradd -u 1005 -g $TDENGINE_GROUP -r -s /bin/false -d /var/lib/taos $TDENGINE_USER
        fi
    "
    
    # 创建目录结构
    remote_execute "$host" "
        mkdir -p $TDENGINE_HOME
        mkdir -p $TDENGINE_DATA_DIR/{data,log,backup}
        mkdir -p $TDENGINE_LOG_DIR
        mkdir -p $TDENGINE_CONFIG_DIR
        mkdir -p /var/lib/taos
        
        chown -R $TDENGINE_USER:$TDENGINE_GROUP $TDENGINE_HOME $TDENGINE_DATA_DIR
        chown -R $TDENGINE_USER:$TDENGINE_GROUP $TDENGINE_LOG_DIR $TDENGINE_CONFIG_DIR
        chown -R $TDENGINE_USER:$TDENGINE_GROUP /var/lib/taos
        chmod 755 $TDENGINE_HOME $TDENGINE_DATA_DIR $TDENGINE_LOG_DIR
    "
    
    # 安装依赖包
    install_system_packages "$host" \
        "gcc" "gcc-c++" "make" "cmake" "git" \
        "glibc-devel" "glibc-static" "libstdc++-static"
    
    log_info "TDEngine环境准备完成: $host"
}

install_tdengine() {
    local host=$1
    
    log_info "在 $host 上安装TDEngine..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上安装TDEngine"
        return 0
    fi
    
    # 检查是否已安装
    if remote_execute "$host" "test -f $TDENGINE_HOME/bin/taosd" 2>/dev/null; then
        if [[ "$FORCE_REINSTALL" != "true" ]]; then
            log_info "TDEngine已安装在 $host，跳过安装"
            return 0
        else
            log_info "强制重新安装TDEngine在 $host"
        fi
    fi
    
    # 安装TDEngine
    remote_execute "$host" "
        cd $TDENGINE_SOURCE_DIR
        
        # 查找安装包
        if [[ -f TDengine-server-*******-Linux-x64.tar.gz ]]; then
            tar -zxf TDengine-server-*******-Linux-x64.tar.gz
            mv TDengine-server-*******/*  $TDENGINE_HOME
            cd $TDENGINE_HOME
            # 执行安装脚本
            ./install.sh -e no
            tar -zxf taos.tar.gz
        elif [[ -f TDengine-*.rpm ]]; then
            # 使用RPM包安装
            rpm -ivh TDengine-*.rpm
        else
            echo '错误: 找不到TDEngine安装包'
            exit 1
        fi
        
        # 设置环境变量
        echo 'export TAOS_HOME=$TDENGINE_HOME' >> /etc/profile
        echo 'export PATH=\$PATH:\$TAOS_HOME/bin' >> /etc/profile
        
        # 验证安装
        $TDENGINE_HOME/bin/taosd -V
    "
    
    log_info "TDEngine安装完成: $host"
}

# =============================================================================
# TDEngine配置函数
# =============================================================================

generate_tdengine_config() {
    local host=$1
    local node_id=$2
    
    log_info "为 $host 生成TDEngine配置文件 (节点ID: $node_id)..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将为 $host 生成TDEngine配置文件"
        return 0
    fi
    
    # 构建集群节点列表
    local first_ep="${TDENGINE_HOSTS[0]}:$TDENGINE_PORT"
    local fqdn_list=""
    for tdengine_host in "${TDENGINE_HOSTS[@]}"; do
        if [[ -n "$fqdn_list" ]]; then
            fqdn_list+=","
        fi
        fqdn_list+="$tdengine_host"
    done
    
    # 生成TDEngine配置文件
    local config_content="# TDEngine配置文件 - $host
# 基础配置
firstEp                   $first_ep
fqdn                      $host
serverPort                $TDENGINE_PORT
httpPort                  6041
rpcMaxTime                600

# 集群配置
numOfMnodes               3
replica                   3
quorum                    2
balance                   1
mnodeEqualVnodeNum        4
offlineThreshold          8640000
statusInterval            1
arbitrator                

# 存储配置
dataDir                   $TDENGINE_DATA_DIR/data
logDir                    $TDENGINE_LOG_DIR
tempDir                   /tmp/
maxShellConns             5000
maxConnections            5000

# 内存配置
ratioOfQueryCores         1.0
maxNumOfDistinctRes       100000
maxVgroupsPerDb           1000
maxTablesPerVnode         1000000

# 日志配置
logKeepDays               30
debugFlag                 131
mDebugFlag                131
dDebugFlag                131
vDebugFlag                131
cDebugFlag                131
jniDebugFlag              131
tmrDebugFlag              131
uDebugFlag                131
rpcDebugFlag              131
qDebugFlag                131
wDebugFlag                131
sDebugFlag                131
tsdbDebugFlag             131

# 性能优化
maxSQLLength              1048576
maxWildCardsLength        100
maxRegexStringLen         128
maxNumOfOrderedRes        100000
keepColumnName            0
timezone                  Asia/Shanghai
locale                    en_US.UTF-8
charset                   UTF-8
maxBinaryDisplayWidth     30
maxConnections            5000
"
    
    # 写入配置文件
    remote_execute "$host" "cat > $TDENGINE_CONFIG_DIR/taos.cfg << 'EOF'
$config_content
EOF"
    
    log_info "TDEngine配置文件生成完成: $host"
}

create_tdengine_service() {
    local host=$1
    
    log_info "在 $host 上创建TDEngine systemd服务..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上创建TDEngine服务"
        return 0
    fi
    
    local service_content="[Unit]
Description=TDEngine time-series database
After=network-online.target
Wants=network-online.target

[Service]
Type=simple
User=$TDENGINE_USER
Group=$TDENGINE_GROUP
ExecStart=$TDENGINE_HOME/bin/taosd -c $TDENGINE_CONFIG_DIR
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
LimitNOFILE=65536
LimitNPROC=32768
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target"
    
    remote_execute "$host" "
        cat > /etc/systemd/system/taosd.service << 'EOF'
$service_content
EOF
        systemctl daemon-reload
        systemctl enable taosd
    "
    
    log_info "TDEngine服务创建完成: $host"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始TDEngine集群部署..."
    
    # 获取锁
    if ! acquire_lock "deploy_tdengine"; then
        log_error "无法获取锁，可能有其他TDEngine部署实例正在运行"
        exit 1
    fi
    
    # 检查TDEngine主机配置
    if [[ ${#TDENGINE_HOSTS[@]} -eq 0 ]]; then
        log_error "未配置TDEngine主机"
        exit 1
    fi
    
    log_info "TDEngine集群主机: ${TDENGINE_HOSTS[*]}"

    # 安装阶段
    if [[ "$SKIP_INSTALL" != "true" ]]; then
        for host in "${TDENGINE_HOSTS[@]}"; do
            if remote_execute "$host" "test -f $TDENGINE_HOME/bin/taosd" 2>/dev/null; then
                log_info "TDEngine已安装在 $host 上"
            else
              log_info "开始安装TDEngine到 $host 上..."
              prepare_tdengine_environment "$host"
              install_tdengine "$host"
              # 配置阶段
              if [[ "$SKIP_CONFIG" != "true" ]]; then
                  local node_id=1
                  generate_tdengine_config "$host" "$node_id"
                  create_tdengine_service "$host"
                  node_id=$((node_id + 1))
              else
                  log_warn "跳过TDEngine配置"
              fi
            fi

        done
    else
        log_warn "跳过TDEngine安装"
    fi

    local all_started=true
    for host in "${TDENGINE_HOSTS[@]}"; do
        if ! check_TDEngine_started "$host"; then
            all_started=false
            log_info "$host 节点未检测到完整TDEngine安装"
        fi
    done

    if [[ "$all_started" == "true" ]]; then
        release_lock "deploy_tdengine"
        exit 0
    fi

    # 启动和初始化阶段
    if [[ "$SKIP_INIT" != "true" ]]; then
        start_tdengine_cluster
        initialize_tdengine_cluster
        verify_tdengine_cluster
    else
        log_warn "跳过TDEngine集群初始化"
    fi

    log_info "TDEngine集群部署完成"
}

check_TDEngine_started() {
    local host=$1
    local service_running=false

    # 检查服务是否运行
    if remote_execute "$host" "systemctl is-active --quiet taosd"; then
        log_info "TDEngine服务已在运行: $host"
        service_running=true
    fi

    # 如果服务已完整安装
    if [[ "$service_running" == "true" ]]; then
        return 0  # 已安装
    fi

    return 1  # 未安装
}

# =============================================================================
# TDEngine集群管理函数
# =============================================================================

start_tdengine_cluster() {
    log_info "启动TDEngine集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将启动TDEngine集群"
        return 0
    fi

    # 启动所有TDEngine节点
    for host in "${TDENGINE_HOSTS[@]}"; do
        log_info "启动TDEngine服务: $host"
        start_service "$host" "taosd"

        # 等待服务启动
        if ! wait_for_port "$host" "$TDENGINE_PORT" 60; then
            log_error "TDEngine服务启动失败: $host"
            return 1
        fi
    done

    # 等待集群稳定
    log_info "等待TDEngine集群稳定..."
    sleep 30

    log_info "TDEngine集群启动完成"
}

initialize_tdengine_cluster() {
    log_info "初始化TDEngine集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将初始化TDEngine集群"
        return 0
    fi

    local first_host="${TDENGINE_HOSTS[0]}"

    # 在第一个节点上初始化集群
    log_info "在 $first_host 上初始化TDEngine集群..."

    # 添加其他节点到集群
    for host in "${TDENGINE_HOSTS[@]:1}"; do
        log_info "添加节点到集群: $host"
        remote_execute "$first_host" "
            $TDENGINE_HOME/bin/taos -h $first_host -s 'CREATE DNODE \"$host:$TDENGINE_PORT\";'
        "
        sleep 5
    done

    # 等待节点加入
    log_info "等待节点加入集群..."
    sleep 30

    # 显示集群状态
    remote_execute "$first_host" "
        $TDENGINE_HOME/bin/taos -h $first_host -s 'SHOW DNODES;'
    "

    log_info "TDEngine集群初始化完成"
}

verify_tdengine_cluster() {
    log_info "验证TDEngine集群..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将验证TDEngine集群"
        return 0
    fi

    local first_host="${TDENGINE_HOSTS[0]}"

    # 创建测试数据库
    remote_execute "$first_host" "
        $TDENGINE_HOME/bin/taos -h $first_host -s '
        CREATE DATABASE IF NOT EXISTS test_db;
        USE test_db;
        CREATE TABLE IF NOT EXISTS test_table (ts TIMESTAMP, va FLOAT);
        INSERT INTO test_table VALUES (NOW, 1.0);
        SELECT * FROM test_table;
        DROP TABLE test_table;
        DROP DATABASE test_db
        '
    "

    # 检查集群状态
    if remote_execute "$first_host" "
        $TDENGINE_HOME/bin/taos -h $first_host -s 'SHOW DNODES;' | grep -q 'ready'
    "; then
        log_info "TDEngine集群验证成功"
    else
        log_error "TDEngine集群验证失败"
        return 1
    fi

    log_info "TDEngine集群验证完成"
}

# 执行主函数
main "$@"
