#!/bin/bash
# 主机配置文件 - 工业场景基础服务集群主机规划
# 遵循DevOps最佳实践，使用数组和关联数组管理主机信息

# =============================================================================
# 主机网络配置
# =============================================================================

# 网络段配置
export NETWORK_SEGMENT="10.81.12"
export NETMASK="*************"
export GATEWAY="**********"

# =============================================================================
# MongoDB集群主机配置
# =============================================================================

# MongoDB主机列表（副本集：1主2从）
declare -a MONGODB_HOSTS=(
    "***********"  # mongodb-01 (Primary)
    "***********"  # mongodb-02 (Secondary)
    "***********"  # mongodb-03 (Secondary)
)

# MongoDB主机名映射
declare -A MONGODB_HOSTNAMES=(
    ["***********"]="mongodb-01"
    ["***********"]="mongodb-02"
    ["***********"]="mongodb-03"
)

# MongoDB角色配置
declare -A MONGODB_ROLES=(
    ["***********"]="primary"
    ["***********"]="secondary"
    ["***********"]="secondary"
)

# MongoDB优先级配置
declare -A MONGODB_PRIORITIES=(
    ["***********"]="2"
    ["***********"]="1"
    ["***********"]="1"
)

# =============================================================================
# Redis集群主机配置
# =============================================================================

# Redis主机列表（集群模式：3主3从）
declare -a REDIS_HOSTS=(
    "***********"  # redis-01 (Master-1)
    "***********"  # redis-02 (Master-2)
    "***********"  # redis-03 (Master-3)
    "***********"  # redis-04 (Slave-1)
    "***********"  # redis-05 (Slave-2)
    "***********"  # redis-06 (Slave-3)
)

# Redis主机名映射
declare -A REDIS_HOSTNAMES=(
    ["***********"]="redis-01"
    ["***********"]="redis-02"
    ["***********"]="redis-03"
    ["***********"]="redis-04"
    ["***********"]="redis-05"
    ["***********"]="redis-06"
)

# Redis端口配置
declare -A REDIS_PORTS=(
    ["***********"]="7001"
    ["***********"]="7003"
    ["***********"]="7005"
    ["***********"]="7002"
    ["***********"]="7004"
    ["***********"]="7006"
)

# Redis角色配置
declare -A REDIS_ROLES=(
    ["***********"]="master"
    ["***********"]="master"
    ["***********"]="master"
    ["***********"]="slave"
    ["***********"]="slave"
    ["***********"]="slave"
)

# =============================================================================
# Kafka集群主机配置
# =============================================================================

# Kafka主机列表（3节点集群）
declare -a KAFKA_HOSTS=(
    "***********"  # kafka-01
    "***********"  # kafka-02
    "***********"  # kafka-03
)

# Kafka主机名映射
declare -A KAFKA_HOSTNAMES=(
    ["***********"]="kafka-01"
    ["***********"]="kafka-02"
    ["***********"]="kafka-03"
)

# Kafka Broker ID配置
declare -A KAFKA_BROKER_IDS=(
    ["***********"]="1"
    ["***********"]="2"
    ["***********"]="3"
)

# =============================================================================
# TDEngine集群主机配置
# =============================================================================

# TDEngine主机列表（3节点分布式集群）
declare -a TDENGINE_HOSTS=(
    "***********"  # tdengine-01
    "***********"  # tdengine-02
    "***********"  # tdengine-03
)

# TDEngine主机名映射
declare -A TDENGINE_HOSTNAMES=(
    ["***********"]="tdengine-01"
    ["***********"]="tdengine-02"
    ["***********"]="tdengine-03"
)

# TDEngine节点ID配置
declare -A TDENGINE_NODE_IDS=(
    ["***********"]="1"
    ["***********"]="2"
    ["***********"]="3"
)

# =============================================================================
# NebulaGraph集群主机配置
# =============================================================================

# NebulaGraph主机列表（3节点分布式集群）
declare -a NEBULA_HOSTS=(
    "***********"  # nebula-01
    "***********"  # nebula-02
    "***********"  # nebula-03
)

# NebulaGraph主机名映射
declare -A NEBULA_HOSTNAMES=(
    ["***********"]="nebula-01"
    ["***********"]="nebula-02"
    ["***********"]="nebula-03"
)

# NebulaGraph服务分布配置
declare -A NEBULA_SERVICES=(
    ["***********"]="meta,storage,graph"
    ["***********"]="meta,storage,graph"
    ["***********"]="meta,storage,graph"
)

# =============================================================================
# 达梦数据库集群主机配置
# =============================================================================

# 达梦数据库主机列表（1主2从）
declare -a DAMENG_HOSTS=(
    "***************"  # dm-01 (Primary)
    "***************"  # dm-02 (Standby)
)

# 达梦数据库主机名映射
declare -A DAMENG_HOSTNAMES=(
    ["***************"]="dm-01"
    ["***************"]="dm-02"
    ["***************"]="dm-03"
)

# 达梦数据库角色配置
declare -A DAMENG_ROLES=(
    ["***************"]="primary"
    ["***************"]="standby"
    ["***************"]="standby"
)

# =============================================================================
# 监控服务主机配置
# =============================================================================

# 监控服务主机列表（主备模式）
declare -a MONITOR_HOSTS=(
    "***************"  # monitor-01 (Prometheus + AlertManager)
    "***************"  # monitor-02 (Grafana + Backup)
)

# 监控服务主机名映射
declare -A MONITOR_HOSTNAMES=(
    ["***************"]="monitor-01"
    ["***************"]="monitor-02"
)

# 监控服务分布配置
declare -A MONITOR_SERVICES=(
    ["***************"]="prometheus,alertmanager"
    ["***************"]="grafana,backup"
)

# =============================================================================
# 负载均衡器配置
# =============================================================================

# 负载均衡器主机列表
declare -a LB_HOSTS=(
    "***************"  # lb-01
    "***************"  # lb-02
)

# 负载均衡器主机名映射
declare -A LB_HOSTNAMES=(
    ["***************"]="lb-01"
    ["***************"]="lb-02"
)

# =============================================================================
# 堡垒机配置
# =============================================================================

# 堡垒机主机
export BASTION_HOST="***********"
export BASTION_HOSTNAME="bastion-01"
export BASTION_USER="admin"

# =============================================================================
# 硬件配置要求
# =============================================================================

# MongoDB硬件配置
declare -A MONGODB_HARDWARE=(
    ["cpu"]="16"
    ["memory"]="32GB"
    ["storage"]="500GB SSD"
    ["network"]="1Gbps"
)

# Redis硬件配置
declare -A REDIS_HARDWARE=(
    ["cpu"]="8"
    ["memory"]="16GB"
    ["storage"]="200GB SSD"
    ["network"]="1Gbps"
)

# Kafka硬件配置
declare -A KAFKA_HARDWARE=(
    ["cpu"]="8"
    ["memory"]="16GB"
    ["storage"]="500GB SSD"
    ["network"]="1Gbps"
)

# TDEngine硬件配置
declare -A TDENGINE_HARDWARE=(
    ["cpu"]="16"
    ["memory"]="32GB"
    ["storage"]="1TB SSD"
    ["network"]="1Gbps"
)

# NebulaGraph硬件配置
declare -A NEBULA_HARDWARE=(
    ["cpu"]="16"
    ["memory"]="32GB"
    ["storage"]="500GB SSD"
    ["network"]="1Gbps"
)

# 达梦数据库硬件配置
declare -A DAMENG_HARDWARE=(
    ["cpu"]="16"
    ["memory"]="64GB"
    ["storage"]="1TB SSD"
    ["network"]="1Gbps"
)

# =============================================================================
# 工具函数
# =============================================================================

# 获取所有主机列表
get_all_hosts() {
    local all_hosts=()
    all_hosts+=("${MONGODB_HOSTS[@]}")
    all_hosts+=("${REDIS_HOSTS[@]}")
    all_hosts+=("${KAFKA_HOSTS[@]}")
    all_hosts+=("${TDENGINE_HOSTS[@]}")
    all_hosts+=("${NEBULA_HOSTS[@]}")
    all_hosts+=("${DAMENG_HOSTS[@]}")
    all_hosts+=("${MONITOR_HOSTS[@]}")
    all_hosts+=("${LB_HOSTS[@]}")
    
    # 去重并排序
    printf '%s\n' "${all_hosts[@]}" | sort -u
}

# 根据IP获取主机名
get_hostname_by_ip() {
    local ip=$1
    local hostname=""
    
    # 检查各个服务的主机名映射
    for service in MONGODB REDIS KAFKA TDENGINE NEBULA DAMENG MONITOR LB; do
        local -n hostnames="${service}_HOSTNAMES"
        if [[ -n "${hostnames[$ip]}" ]]; then
            hostname="${hostnames[$ip]}"
            break
        fi
    done
    
    echo "$hostname"
}

# 根据IP获取服务类型
get_service_by_ip() {
    local ip=$1
    local services=()
    
    # 检查IP属于哪个服务
    for host in "${MONGODB_HOSTS[@]}"; do
        [[ "$host" == "$ip" ]] && services+=("mongodb")
    done
    
    for host in "${REDIS_HOSTS[@]}"; do
        [[ "$host" == "$ip" ]] && services+=("redis")
    done
    
    for host in "${KAFKA_HOSTS[@]}"; do
        [[ "$host" == "$ip" ]] && services+=("kafka")
    done
    
    for host in "${TDENGINE_HOSTS[@]}"; do
        [[ "$host" == "$ip" ]] && services+=("tdengine")
    done
    
    for host in "${NEBULA_HOSTS[@]}"; do
        [[ "$host" == "$ip" ]] && services+=("nebula")
    done
    
#    for host in "${DAMENG_HOSTS[@]}"; do
#        [[ "$host" == "$ip" ]] && services+=("dameng")
#    done
    
#    for host in "${MONITOR_HOSTS[@]}"; do
#        [[ "$host" == "$ip" ]] && services+=("monitor")
#    done
    
    printf '%s\n' "${services[@]}"
}

# 验证主机配置
validate_hosts_config() {
    local errors=0
    
    # 检查主机数量
    if [[ ${#MONGODB_HOSTS[@]} -ne 3 ]]; then
        echo "错误: MongoDB集群需要3个节点" >&2
        errors=$((errors + 1))
    fi
    
    if [[ ${#REDIS_HOSTS[@]} -ne 6 ]]; then
        echo "错误: Redis集群需要6个节点" >&2
        errors=$((errors + 1))
    fi
    
    if [[ ${#KAFKA_HOSTS[@]} -ne 3 ]]; then
        echo "错误: Kafka集群需要3个节点" >&2
        errors=$((errors + 1))
    fi
    
    # 检查IP地址格式
    local all_hosts
    mapfile -t all_hosts < <(get_all_hosts)
    
    for host in "${all_hosts[@]}"; do
        if ! [[ $host =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
            echo "错误: 无效的IP地址格式: $host" >&2
            errors=$((errors + 1))
        fi
    done
    
    return $errors
}

# 输出主机配置摘要
print_hosts_summary() {
    echo "=== 主机配置摘要 ==="
    echo "MongoDB集群: ${#MONGODB_HOSTS[@]} 节点"
    echo "Redis集群: ${#REDIS_HOSTS[@]} 节点"
    echo "Kafka集群: ${#KAFKA_HOSTS[@]} 节点"
    echo "TDEngine集群: ${#TDENGINE_HOSTS[@]} 节点"
    echo "NebulaGraph集群: ${#NEBULA_HOSTS[@]} 节点"
    echo "达梦数据库集群: ${#DAMENG_HOSTS[@]} 节点"
    echo "监控服务: ${#MONITOR_HOSTS[@]} 节点"
    echo "负载均衡器: ${#LB_HOSTS[@]} 节点"
    echo "总计: $(get_all_hosts | wc -l) 个主机"
    echo "===================="
}

# 初始化验证
if ! validate_hosts_config; then
    echo "主机配置验证失败，请检查配置" >&2
    exit 1
fi
