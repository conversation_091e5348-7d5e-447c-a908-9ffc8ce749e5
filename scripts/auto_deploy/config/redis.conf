#!/bin/bash
# Redis配置文件 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，Redis集群配置

# =============================================================================
# Redis基础配置
# =============================================================================

# Redis版本和端口
export REDIS_VERSION="7.0.8"
export REDIS_CLUSTER_PORTS="7001,7002,7003,7004,7005,7006"

# Redis内存配置
export REDIS_MAX_MEMORY="8gb"
export REDIS_MAX_MEMORY_POLICY="allkeys-lru"

# Redis持久化配置
export REDIS_SAVE_ENABLED="true"
export REDIS_AOF_ENABLED="true"
export REDIS_AOF_FSYNC="everysec"

# =============================================================================
# Redis集群配置
# =============================================================================

# 集群基础配置
export REDIS_CLUSTER_ENABLED="true"
export REDIS_CLUSTER_NODE_TIMEOUT="15000"  # ms
export REDIS_CLUSTER_REQUIRE_FULL_COVERAGE="no"

# 集群故障转移配置
export REDIS_CLUSTER_MIGRATION_BARRIER="1"
export REDIS_CLUSTER_REPLICA_VALIDITY_FACTOR="10"

# =============================================================================
# Redis性能配置
# =============================================================================

# 网络配置
export REDIS_TCP_KEEPALIVE="300"
export REDIS_TCP_BACKLOG="511"
export REDIS_TIMEOUT="0"

# 客户端配置
export REDIS_MAX_CLIENTS="10000"
export REDIS_CLIENT_OUTPUT_BUFFER_LIMIT="normal 0 0 0"

# 内存优化
export REDIS_HASH_MAX_ZIPLIST_ENTRIES="512"
export REDIS_HASH_MAX_ZIPLIST_VALUE="64"
export REDIS_LIST_MAX_ZIPLIST_SIZE="-2"
export REDIS_SET_MAX_INTSET_ENTRIES="512"
export REDIS_ZSET_MAX_ZIPLIST_ENTRIES="128"
export REDIS_ZSET_MAX_ZIPLIST_VALUE="64"

# =============================================================================
# Redis安全配置
# =============================================================================

# 认证配置
export REDIS_AUTH_ENABLED="true"
export REDIS_REQUIREPASS="$(openssl rand -base64 32)"
export REDIS_MASTERAUTH="$REDIS_REQUIREPASS"

# 网络安全
export REDIS_BIND_ADDRESS="0.0.0.0"
export REDIS_PROTECTED_MODE="no"

# 命令重命名（安全加固）
export REDIS_RENAME_COMMANDS="true"
declare -A REDIS_COMMAND_RENAMES=(
    ["FLUSHDB"]="FLUSHDB_$(openssl rand -hex 8)"
    ["FLUSHALL"]="FLUSHALL_$(openssl rand -hex 8)"
    ["CONFIG"]="CONFIG_$(openssl rand -hex 8)"
    ["DEBUG"]="DEBUG_$(openssl rand -hex 8)"
)

# =============================================================================
# Redis日志配置
# =============================================================================

# 日志级别和格式
export REDIS_LOG_LEVEL="notice"
export REDIS_SYSLOG_ENABLED="no"

# 慢查询日志
export REDIS_SLOWLOG_LOG_SLOWER_THAN="10000"  # 微秒
export REDIS_SLOWLOG_MAX_LEN="128"

# =============================================================================
# Redis持久化配置
# =============================================================================

# RDB配置
export REDIS_RDB_COMPRESSION="yes"
export REDIS_RDB_CHECKSUM="yes"
export REDIS_SAVE_POINTS="900 1 300 10 60 10000"

# AOF配置
export REDIS_AOF_REWRITE_PERCENTAGE="100"
export REDIS_AOF_REWRITE_MIN_SIZE="64mb"
export REDIS_NO_APPENDFSYNC_ON_REWRITE="no"

# =============================================================================
# Redis监控配置
# =============================================================================

# 监控参数
export REDIS_MONITORING_ENABLED="true"
export REDIS_EXPORTER_PORT="9121"
export REDIS_METRICS_COLLECTION_INTERVAL="15"  # seconds

# 告警阈值
export REDIS_CPU_THRESHOLD="80"        # 百分比
export REDIS_MEMORY_THRESHOLD="85"     # 百分比
export REDIS_CONNECTION_THRESHOLD="80" # 百分比
export REDIS_KEYSPACE_HITS_RATIO="0.9" # 命中率

# =============================================================================
# Redis备份配置
# =============================================================================

# 备份策略
export REDIS_BACKUP_ENABLED="true"
export REDIS_BACKUP_SCHEDULE="0 3 * * *"  # 每天凌晨3点
export REDIS_BACKUP_RETENTION_DAYS="7"
export REDIS_BACKUP_COMPRESSION="true"

# 备份路径
export REDIS_BACKUP_PATH="/data/redis/backup"
export REDIS_BACKUP_SCRIPT="/apps/scripts/redis_backup.sh"

# =============================================================================
# Redis高可用配置
# =============================================================================

# 哨兵配置（如果使用哨兵模式）
export REDIS_SENTINEL_ENABLED="false"
export REDIS_SENTINEL_PORT="26379"
export REDIS_SENTINEL_QUORUM="2"
export REDIS_SENTINEL_DOWN_AFTER="30000"  # ms
export REDIS_SENTINEL_FAILOVER_TIMEOUT="180000"  # ms

# 主从复制配置
export REDIS_REPLICATION_ENABLED="true"
export REDIS_REPLICA_READ_ONLY="yes"
export REDIS_REPLICA_SERVE_STALE_DATA="yes"

# =============================================================================
# Redis集群节点配置
# =============================================================================

# 将端口分配给主机
IFS=',' read -ra REDIS_PORTS <<< "$REDIS_CLUSTER_PORTS"

# Redis主机和端口映射
declare -A REDIS_HOST_PORT_MAP=()
local port_index=0
for host in "${REDIS_HOSTS[@]}"; do
    if [[ $port_index -lt ${#REDIS_PORTS[@]} ]]; then
        REDIS_HOST_PORT_MAP["$host"]="${REDIS_PORTS[$port_index]}"
        port_index=$((port_index + 1))
    fi
done

# =============================================================================
# Redis配置验证函数
# =============================================================================

# 验证Redis配置
validate_redis_config() {
    local errors=0
    
    # 检查必需的Redis参数
    local required_vars=(
        "REDIS_VERSION" "REDIS_CLUSTER_PORTS" "REDIS_MAX_MEMORY"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            echo "错误: 必需的Redis变量 $var 未设置" >&2
            errors=$((errors + 1))
        fi
    done
    
    # 检查Redis主机数量
    if [[ ${#REDIS_HOSTS[@]} -lt 6 ]]; then
        echo "警告: Redis集群建议至少6个节点（3主3从）以保证高可用"
    fi
    
    # 检查端口数量
    IFS=',' read -ra ports <<< "$REDIS_CLUSTER_PORTS"
    if [[ ${#ports[@]} -ne ${#REDIS_HOSTS[@]} ]]; then
        echo "错误: Redis端口数量与主机数量不匹配" >&2
        errors=$((errors + 1))
    fi
    
    return $errors
}

# 生成Redis集群节点列表
generate_redis_cluster_nodes() {
    local nodes=()
    local port_index=0
    
    for host in "${REDIS_HOSTS[@]}"; do
        if [[ $port_index -lt ${#REDIS_PORTS[@]} ]]; then
            nodes+=("$host:${REDIS_PORTS[$port_index]}")
            port_index=$((port_index + 1))
        fi
    done
    
    echo "${nodes[*]}"
}

# 获取Redis主节点列表
get_redis_master_nodes() {
    local masters=()
    local port_index=0
    local master_count=$((${#REDIS_HOSTS[@]} / 2))
    
    for ((i=0; i<master_count; i++)); do
        if [[ $port_index -lt ${#REDIS_PORTS[@]} ]]; then
            masters+=("${REDIS_HOSTS[$i]}:${REDIS_PORTS[$port_index]}")
            port_index=$((port_index + 1))
        fi
    done
    
    echo "${masters[*]}"
}

# =============================================================================
# Redis配置初始化
# =============================================================================

# 验证Redis配置
if ! validate_redis_config; then
    echo "Redis配置验证失败，请检查配置文件" >&2
    exit 1
fi

# 输出Redis配置信息
echo "Redis配置加载完成"
echo "Redis版本: $REDIS_VERSION"
echo "集群端口: $REDIS_CLUSTER_PORTS"
echo "最大内存: $REDIS_MAX_MEMORY"
echo "集群节点: $(generate_redis_cluster_nodes)"
echo "主节点: $(get_redis_master_nodes)"
