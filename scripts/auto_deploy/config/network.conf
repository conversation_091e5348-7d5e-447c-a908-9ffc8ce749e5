#!/bin/bash
# 网络配置文件 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，网络参数配置

# =============================================================================
# 网络基础配置
# =============================================================================

# 网络接口配置
export NETWORK_INTERFACE="eth0"
export NETWORK_MTU="1500"

# DNS配置
export DNS_SERVERS="*******,***************,*********"
export DNS_SEARCH_DOMAIN="company.local"

# NTP配置
export NTP_SERVERS="ntp.aliyun.com,time.windows.com,cn.pool.ntp.org"

# =============================================================================
# 负载均衡配置
# =============================================================================

# 负载均衡器配置
export ENABLE_LOAD_BALANCER="true"
export LB_ALGORITHM="round_robin"
export LB_HEALTH_CHECK_INTERVAL="30"

# VIP配置（虚拟IP）
export MONGODB_VIP="*************"
export REDIS_VIP="*************"
export KAFKA_VIP="*************"
export TDENGINE_VIP="*************"
export NEBULA_VIP="*************"
export DAMENG_VIP="*************"
export MONITOR_VIP="*************"

# =============================================================================
# 网络安全配置
# =============================================================================

# 防火墙端口配置
export ALLOWED_PORTS=(
    "22"        # SSH
    "27017"     # MongoDB
    "6379"      # Redis
    "7001-7006" # Redis Cluster
    "9092"      # Kafka
    "2181"      # ZooKeeper
    "6030"      # TDEngine
    "9669"      # NebulaGraph Graph
    "9559"      # NebulaGraph Meta
    "9779"      # NebulaGraph Storage
    "5236"      # DaMeng Database
    "9090"      # Prometheus
    "3000"      # Grafana
    "9093"      # AlertManager
    "9100"      # Node Exporter
)

# 网络访问控制
export ALLOWED_NETWORKS=(
    "***********/16"
    "10.0.0.0/8"
    "**********/12"
)

# =============================================================================
# 网络优化参数
# =============================================================================

# TCP参数优化
export TCP_KEEPALIVE_TIME="1200"
export TCP_KEEPALIVE_PROBES="3"
export TCP_KEEPALIVE_INTVL="15"
export TCP_FIN_TIMEOUT="30"

# 网络缓冲区优化
export NET_CORE_RMEM_DEFAULT="262144"
export NET_CORE_RMEM_MAX="16777216"
export NET_CORE_WMEM_DEFAULT="262144"
export NET_CORE_WMEM_MAX="16777216"
export NET_CORE_NETDEV_MAX_BACKLOG="5000"

# =============================================================================
# 服务发现配置
# =============================================================================

# 服务注册中心配置
export ENABLE_SERVICE_DISCOVERY="false"
export SERVICE_REGISTRY_TYPE="consul"  # consul, etcd, zookeeper
export SERVICE_REGISTRY_HOSTS="*************:8500"

# 健康检查配置
export HEALTH_CHECK_INTERVAL="30"
export HEALTH_CHECK_TIMEOUT="10"
export HEALTH_CHECK_RETRIES="3"

# =============================================================================
# 网络监控配置
# =============================================================================

# 网络监控参数
export ENABLE_NETWORK_MONITORING="true"
export NETWORK_MONITOR_INTERVAL="60"
export BANDWIDTH_THRESHOLD="80"  # 百分比
export LATENCY_THRESHOLD="100"   # 毫秒

# SNMP配置
export ENABLE_SNMP="false"
export SNMP_COMMUNITY="public"
export SNMP_VERSION="2c"

# =============================================================================
# 网络故障恢复配置
# =============================================================================

# 故障检测配置
export NETWORK_FAILURE_DETECTION="true"
export PING_CHECK_INTERVAL="10"
export PING_CHECK_TIMEOUT="5"
export PING_CHECK_COUNT="3"

# 自动恢复配置
export AUTO_RECOVERY_ENABLED="true"
export RECOVERY_RETRY_COUNT="3"
export RECOVERY_RETRY_INTERVAL="30"

# =============================================================================
# 网络配置验证函数
# =============================================================================

# 验证网络配置
validate_network_config() {
    local errors=0
    
    # 检查必需的网络参数
    local required_vars=(
        "NETWORK_INTERFACE" "DNS_SERVERS" "NTP_SERVERS"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            echo "错误: 必需的网络变量 $var 未设置" >&2
            errors=$((errors + 1))
        fi
    done
    
    # 检查网络接口是否存在
    if ! ip link show "$NETWORK_INTERFACE" >/dev/null 2>&1; then
        echo "警告: 网络接口 $NETWORK_INTERFACE 不存在"
    fi
    
    return $errors
}

# 获取网络接口IP地址
get_interface_ip() {
    local interface=${1:-$NETWORK_INTERFACE}
    ip addr show "$interface" | grep 'inet ' | awk '{print $2}' | cut -d/ -f1 | head -1
}

# 检查网络连通性
check_network_connectivity() {
    local target_host=$1
    local timeout=${2:-5}
    
    ping -c 3 -W "$timeout" "$target_host" >/dev/null 2>&1
    return $?
}

# 检查端口连通性
check_port_connectivity() {
    local host=$1
    local port=$2
    local timeout=${3:-5}
    
    timeout "$timeout" bash -c "</dev/tcp/$host/$port" 2>/dev/null
    return $?
}

# =============================================================================
# 网络配置初始化
# =============================================================================

# 验证网络配置
if ! validate_network_config; then
    echo "网络配置验证失败，请检查配置文件" >&2
    exit 1
fi

# 输出网络配置信息
echo "网络配置加载完成"
echo "网络接口: $NETWORK_INTERFACE"
echo "DNS服务器: $DNS_SERVERS"
echo "NTP服务器: $NTP_SERVERS"
echo "负载均衡: $ENABLE_LOAD_BALANCER"
