#!/bin/bash
# 全局配置文件 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，使用环境变量和配置文件管理

# =============================================================================
# 基础配置
# =============================================================================

# 部署环境配置
export DEPLOY_ENV="production"
export DEPLOY_USER="root"
export DEPLOY_KEY="/root/.ssh/id_rsa"
export DEPLOY_TIMEOUT="300"

# 部署模式配置
export OFFLINE_MODE="true"  # true=离线模式, false=在线模式
export INTERNET_ACCESS="false"  # 是否可以访问外网
export USE_LOCAL_REPO="true"  # 是否使用本地软件仓库

# 离线部署配置（仅在离线模式下使用）
export LOCAL_YUM_REPO="/apps/offline-prep/yum-repo"
export LOCAL_PIP_REPO="/apps/offline-prep/pip-repo"
export LOCAL_DOCKER_REGISTRY=""
export OFFLINE_PACKAGE_DIR="/apps/offline-packages"

# 软件仓库配置
export SOFTWARE_REPO="/apps/software-repo"
export OFFLINE_PREP_DIR="/apps/offline-prep"
export BACKUP_DIR="/backup"
export LOG_DIR="/var/log/deploy"
export SCRIPT_DIR="/apps/scripts"

# 系统环境配置
export OS_VERSION="kylin_v10"
export CPU_ARCH="x86_64"
export TIMEZONE="Asia/Shanghai"
export LOCALE="zh_CN.UTF-8"

# =============================================================================
# 安全配置
# =============================================================================

# 防火墙和SELinux配置
export ENABLE_FIREWALL="true"
export ENABLE_SELINUX="false"

# SSH配置
export SSH_PORT="22"
export SSH_TIMEOUT="30"
export SSH_RETRIES="3"

# 密码策略（用于生成随机密码）
export PASSWORD_LENGTH="16"
export PASSWORD_COMPLEXITY="true"

# =============================================================================
# 网络配置
# =============================================================================

# 网络基础配置
export NETWORK_INTERFACE="eth0"
export DNS_SERVERS="***************,*********"  # 使用国内DNS
export NTP_SERVERS="ntp.ntsc.ac.cn,time.nist.gov"  # 离线环境可配置本地NTP

# 负载均衡配置
export ENABLE_LOAD_BALANCER="true"
export LB_ALGORITHM="round_robin"

# =============================================================================
# 存储配置
# =============================================================================

# 磁盘配置
export DATA_DISK="/dev/sdb"
export DATA_MOUNT_POINT="/data"
export FILESYSTEM_TYPE="xfs"

# 备份配置
export BACKUP_RETENTION_DAYS="7"
export BACKUP_COMPRESSION="true"

# =============================================================================
# 监控配置
# =============================================================================

# Prometheus配置
export PROMETHEUS_PORT="9090"
export PROMETHEUS_RETENTION="30d"

# Grafana配置
export GRAFANA_PORT="3000"
export GRAFANA_ADMIN_USER="admin"

# AlertManager配置
export ALERTMANAGER_PORT="9093"
export ALERT_EMAIL="<EMAIL>"
export SMTP_SERVER="smtp.company.com"
export SMTP_PORT="587"

# =============================================================================
# 服务配置
# =============================================================================

# MongoDB配置
export MONGODB_VERSION="4.4.18"
export MONGODB_PORT="27017"
export MONGODB_REPLICA_SET="rs0"
export MONGODB_OPLOG_SIZE="10240"

# Redis配置
export REDIS_VERSION="7.0.8"
export REDIS_CLUSTER_PORTS="7001,7002,7003,7004,7005,7006"
export REDIS_MAX_MEMORY="8gb"

# Kafka配置
export KAFKA_VERSION="2.8.1"
export KAFKA_PORT="9092"
export ZOOKEEPER_PORT="2181"
export KAFKA_REPLICATION_FACTOR="3"

# TDEngine配置
export TDENGINE_VERSION="3.0.2.0"
export TDENGINE_PORT="6030"

# NebulaGraph配置
export NEBULA_VERSION="3.4.0"
export NEBULA_GRAPH_PORT="9669"
export NEBULA_META_PORT="9559"
export NEBULA_STORAGE_PORT="9779"

# 达梦数据库配置
export DAMENG_VERSION="8.1.2.128"
export DAMENG_PORT="5236"

# =============================================================================
# 编译配置
# =============================================================================

# 编译器配置（针对海光CPU优化）
export CC="gcc"
export CXX="g++"
export CFLAGS="-O2 -march=x86-64 -mtune=generic"
export CXXFLAGS="-O2 -march=x86-64 -mtune=generic"

# 海光CPU特殊配置
export PORTABLE="1"
export USE_SSE="OFF"
export USE_AVX="OFF"

# 并发编译配置
export MAKE_JOBS="$(nproc)"
export MAX_MAKE_JOBS="8"

# =============================================================================
# JDK配置
# =============================================================================

# JDK版本和路径
export JAVA_VERSION="11"
export JAVA_HOME="/apps/jdk"
export JDK_VENDOR="bisheng"  # 华为毕昇JDK

# JVM参数优化
export JVM_HEAP_SIZE="4g"
export JVM_METASPACE_SIZE="256m"

# =============================================================================
# 部署流程配置
# =============================================================================

# 部署阶段控制
export DEPLOY_INFRASTRUCTURE="true"
export DEPLOY_DATABASES="true"
export DEPLOY_CACHE="true"
export DEPLOY_MESSAGING="true"
export DEPLOY_MONITORING="true"

# 验证配置
export ENABLE_HEALTH_CHECK="true"
export HEALTH_CHECK_TIMEOUT="60"
export ENABLE_PERFORMANCE_TEST="false"

# 回滚配置
export ENABLE_ROLLBACK="true"
export ROLLBACK_TIMEOUT="300"

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别和格式
export LOG_LEVEL="INFO"
export LOG_FORMAT="json"
export LOG_ROTATION="daily"
export LOG_RETENTION_DAYS="30"

# =============================================================================
# 通知配置
# =============================================================================

# 邮件通知
export ENABLE_EMAIL_NOTIFICATION="true"
export NOTIFICATION_EMAIL="<EMAIL>"

# 钉钉通知
export ENABLE_DINGTALK_NOTIFICATION="false"
export DINGTALK_WEBHOOK=""

# =============================================================================
# 性能调优配置
# =============================================================================

# 系统参数优化
export OPTIMIZE_KERNEL_PARAMS="true"
export OPTIMIZE_NETWORK_PARAMS="true"
export OPTIMIZE_STORAGE_PARAMS="true"

# 内存配置
export HUGEPAGES_ENABLED="false"
export SWAPPINESS="1"

# =============================================================================
# 函数定义
# =============================================================================

# 验证配置函数
validate_config() {
    local errors=0
    
    # 检查必需的环境变量
    local required_vars=(
        "DEPLOY_USER" "SOFTWARE_REPO" "LOG_DIR"
        "MONGODB_VERSION" "REDIS_VERSION" "KAFKA_VERSION"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            echo "错误: 必需的环境变量 $var 未设置" >&2
            errors=$((errors + 1))
        fi
    done
    
    # 检查目录是否存在
    local required_dirs=("$SOFTWARE_REPO" "$BACKUP_DIR")
    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            echo "警告: 目录 $dir 不存在，将自动创建"
            mkdir -p "$dir" || errors=$((errors + 1))
        fi
    done
    
    return $errors
}

# 生成随机密码函数
generate_password() {
    local length=${1:-$PASSWORD_LENGTH}
    if [[ "$PASSWORD_COMPLEXITY" == "true" ]]; then
        openssl rand -base64 $((length * 3 / 4)) | tr -d "=+/" | cut -c1-${length}
    else
        openssl rand -hex $((length / 2))
    fi
}

# 获取主机IP函数
get_host_ip() {
    local interface=${1:-$NETWORK_INTERFACE}
    ip addr show "$interface" | grep 'inet ' | awk '{print $2}' | cut -d/ -f1 | head -1
}

# 检查端口是否可用函数
check_port_available() {
    local host=$1
    local port=$2
    local timeout=${3:-5}
    
    timeout $timeout bash -c "</dev/tcp/$host/$port" 2>/dev/null
    return $?
}

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $*" | tee -a "$LOG_DIR/deploy.log"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $*" | tee -a "$LOG_DIR/deploy.log" >&2
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $*" | tee -a "$LOG_DIR/deploy.log"
}

# =============================================================================
# 初始化配置
# =============================================================================

# 创建必要的目录
mkdir -p "$LOG_DIR" "$SCRIPT_DIR"

# 验证配置
if ! validate_config; then
    echo "配置验证失败，请检查配置文件" >&2
    exit 1
fi

# 输出配置信息
log_info "全局配置加载完成"
log_info "部署环境: $DEPLOY_ENV"
log_info "目标架构: $OS_VERSION ($CPU_ARCH)"
log_info "软件仓库: $SOFTWARE_REPO"
log_info "日志目录: $LOG_DIR"

