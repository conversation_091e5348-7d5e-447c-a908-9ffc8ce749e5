#!/bin/bash
# MongoDB配置文件 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，MongoDB集群配置

# =============================================================================
# MongoDB基础配置
# =============================================================================

# MongoDB版本和端口
export MONGODB_VERSION="4.4.18"
export MONGODB_PORT="27017"

# MongoDB副本集配置
export MONGODB_REPLICA_SET="rs0"
export MONGODB_OPLOG_SIZE="10240"  # MB

# MongoDB存储配置
export MONGODB_STORAGE_ENGINE="wiredTiger"
export MONGODB_CACHE_SIZE_GB="16"
export MONGODB_JOURNAL_COMMIT_INTERVAL="100"  # ms

# =============================================================================
# MongoDB集群角色配置
# =============================================================================

# MongoDB主机角色映射
declare -A MONGODB_ROLES=(
    ["${MONGODB_HOSTS[0]}"]="primary"
    ["${MONGODB_HOSTS[1]}"]="secondary"
    ["${MONGODB_HOSTS[2]}"]="secondary"
)

# MongoDB优先级配置
declare -A MONGODB_PRIORITIES=(
    ["${MONGODB_HOSTS[0]}"]="10"
    ["${MONGODB_HOSTS[1]}"]="5"
    ["${MONGODB_HOSTS[2]}"]="1"
)

# =============================================================================
# MongoDB性能配置
# =============================================================================

# 连接配置
export MONGODB_MAX_CONNECTIONS="2000"
export MONGODB_MAX_INCOMING_CONNECTIONS="2000"

# 内存配置
export MONGODB_CACHE_SIZE="16GB"
export MONGODB_JOURNAL_COMPRESSOR="snappy"

# 索引配置
export MONGODB_INDEX_BUILD_RETRY="true"
export MONGODB_INDEX_PREFIX_COMPRESSION="true"

# =============================================================================
# MongoDB安全配置
# =============================================================================

# 认证配置
export MONGODB_AUTH_ENABLED="true"
export MONGODB_KEYFILE_PATH="/apps/mongodb/conf/mongodb-keyfile"

# SSL配置
export MONGODB_SSL_ENABLED="false"
export MONGODB_SSL_MODE="requireSSL"
export MONGODB_SSL_PEM_KEY_FILE=""
export MONGODB_SSL_CA_FILE=""

# =============================================================================
# MongoDB日志配置
# =============================================================================

# 日志级别和格式
export MONGODB_LOG_LEVEL="1"
export MONGODB_LOG_DESTINATION="file"
export MONGODB_LOG_APPEND="true"
export MONGODB_LOG_ROTATE="reopen"

# 慢查询配置
export MONGODB_SLOW_OP_THRESHOLD="100"  # ms
export MONGODB_PROFILING_MODE="slowOp"

# =============================================================================
# MongoDB备份配置
# =============================================================================

# 备份策略
export MONGODB_BACKUP_ENABLED="true"
export MONGODB_BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点
export MONGODB_BACKUP_RETENTION_DAYS="7"
export MONGODB_BACKUP_COMPRESSION="true"

# 备份路径
export MONGODB_BACKUP_PATH="/data/mongodb/backup"
export MONGODB_BACKUP_SCRIPT="/apps/scripts/mongodb_backup.sh"

# =============================================================================
# MongoDB监控配置
# =============================================================================

# 监控参数
export MONGODB_MONITORING_ENABLED="true"
export MONGODB_EXPORTER_PORT="9216"
export MONGODB_METRICS_COLLECTION_INTERVAL="15"  # seconds

# 告警阈值
export MONGODB_CPU_THRESHOLD="80"        # 百分比
export MONGODB_MEMORY_THRESHOLD="85"     # 百分比
export MONGODB_DISK_THRESHOLD="90"       # 百分比
export MONGODB_CONNECTION_THRESHOLD="80" # 百分比

# =============================================================================
# MongoDB高可用配置
# =============================================================================

# 故障转移配置
export MONGODB_ELECTION_TIMEOUT="10000"     # ms
export MONGODB_HEARTBEAT_INTERVAL="2000"    # ms
export MONGODB_HEARTBEAT_TIMEOUT="10000"    # ms

# 读写分离配置
export MONGODB_READ_PREFERENCE="secondaryPreferred"
export MONGODB_READ_CONCERN="majority"
export MONGODB_WRITE_CONCERN="majority"

# =============================================================================
# MongoDB分片配置（可选）
# =============================================================================

# 分片集群配置
export MONGODB_SHARDING_ENABLED="false"
export MONGODB_CONFIG_SERVERS=""
export MONGODB_MONGOS_PORT="27018"
export MONGODB_SHARD_KEY_PATTERN=""

# =============================================================================
# MongoDB配置验证函数
# =============================================================================

# 验证MongoDB配置
validate_mongodb_config() {
    local errors=0
    
    # 检查必需的MongoDB参数
    local required_vars=(
        "MONGODB_VERSION" "MONGODB_PORT" "MONGODB_REPLICA_SET"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            echo "错误: 必需的MongoDB变量 $var 未设置" >&2
            errors=$((errors + 1))
        fi
    done
    
    # 检查MongoDB主机数量
    if [[ ${#MONGODB_HOSTS[@]} -lt 3 ]]; then
        echo "警告: MongoDB集群建议至少3个节点以保证高可用"
    fi
    
    # 检查副本集名称格式
    if [[ ! "$MONGODB_REPLICA_SET" =~ ^[a-zA-Z][a-zA-Z0-9_]*$ ]]; then
        echo "错误: MongoDB副本集名称格式不正确" >&2
        errors=$((errors + 1))
    fi
    
    return $errors
}

# 生成MongoDB连接字符串
generate_mongodb_connection_string() {
    local username=${1:-""}
    local password=${2:-""}
    local database=${3:-"admin"}
    
    local connection_string="mongodb://"
    
    if [[ -n "$username" && -n "$password" ]]; then
        connection_string+="$username:$password@"
    fi
    
    local hosts_string=""
    for host in "${MONGODB_HOSTS[@]}"; do
        if [[ -n "$hosts_string" ]]; then
            hosts_string+=","
        fi
        hosts_string+="$host:$MONGODB_PORT"
    done
    
    connection_string+="$hosts_string/$database?replicaSet=$MONGODB_REPLICA_SET"
    
    echo "$connection_string"
}

# 获取MongoDB主节点
get_mongodb_primary() {
    local first_host="${MONGODB_HOSTS[0]}"
    
    for host in "${MONGODB_HOSTS[@]}"; do
        if [[ "${MONGODB_ROLES[$host]}" == "primary" ]]; then
            echo "$host"
            return 0
        fi
    done
    
    echo "$first_host"
}

# =============================================================================
# MongoDB配置初始化
# =============================================================================

# 验证MongoDB配置
if ! validate_mongodb_config; then
    echo "MongoDB配置验证失败，请检查配置文件" >&2
    exit 1
fi

# 输出MongoDB配置信息
echo "MongoDB配置加载完成"
echo "MongoDB版本: $MONGODB_VERSION"
echo "MongoDB端口: $MONGODB_PORT"
echo "副本集名称: $MONGODB_REPLICA_SET"
echo "集群节点: ${MONGODB_HOSTS[*]}"
echo "主节点: $(get_mongodb_primary)"
