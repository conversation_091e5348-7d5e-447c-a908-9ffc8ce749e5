#!/bin/bash
# 离线部署配置文件 - 工业场景基础服务自动部署
# 专门用于离线环境的配置参数

# =============================================================================
# 离线模式基础配置
# =============================================================================

# 离线模式开关
export OFFLINE_MODE="true"
export INTERNET_ACCESS="false"
export USE_LOCAL_REPO="true"

# 离线资源路径
export LOCAL_YUM_REPO="/apps/offline-prep/yum-repo"
export LOCAL_PIP_REPO="/apps/offline-prep/pip-repo"
export OFFLINE_PACKAGE_DIR="/apps/offline-prep/software"
export LOCAL_DOCKER_REGISTRY=""

# =============================================================================
# 离线软件包配置
# =============================================================================

# MongoDB离线配置
export MONGODB_OFFLINE_PACKAGE="mongodb-hygon-precompiled.tar.gz"
export MONGODB_SOURCE_PACKAGE="mongodb-src-r4.4.18.tar.gz"
export MONGODB_USE_PRECOMPILED="true"

# Redis离线配置
export REDIS_OFFLINE_PACKAGE="redis-hygon-precompiled.tar.gz"
export REDIS_SOURCE_PACKAGE="redis-7.0.8.tar.gz"
export REDIS_USE_PRECOMPILED="true"

# Kafka离线配置
export KAFKA_PACKAGE="kafka_2.13-3.4.0.tgz"
export KAFKA_ALT_PACKAGE="apache-kafka-3.4.0.tar.gz"

# JDK离线配置
export JDK_PACKAGE="bisheng-jdk-11.0.19-linux-x64.tar.gz"
export JDK_ALT_PACKAGE="openjdk-11.0.19_linux-x64_bin.tar.gz"
export JDK_PREFER_BISHENG="true"

# TDEngine离线配置
export TDENGINE_PACKAGE="TDengine-server-*******.tar.gz"
export TDENGINE_RPM_PACKAGE="TDengine-server-*******.rpm"

# NebulaGraph离线配置
export NEBULA_PACKAGE="nebula-graph-3.5.0.tar.gz"

# 达梦数据库离线配置
export DAMENG_ISO_PACKAGE="dm8_20230808_x86_rh6_64.iso"
export DAMENG_TAR_PACKAGE="dm8_20230808_x86_rh6_64.tar.gz"

# 监控系统离线配置
export PROMETHEUS_PACKAGE="prometheus-2.45.0.linux-amd64.tar.gz"
export GRAFANA_PACKAGE="grafana-10.0.0.linux-amd64.tar.gz"
export ALERTMANAGER_PACKAGE="alertmanager-0.25.0.linux-amd64.tar.gz"

# =============================================================================
# 离线网络配置
# =============================================================================

# DNS配置（离线环境）
export DNS_SERVERS="127.0.0.1"  # 使用本地DNS或内网DNS
export DNS_SEARCH_DOMAIN="local"

# NTP配置（离线环境）
export NTP_SERVERS=""  # 离线环境使用本地时钟
export USE_LOCAL_TIME="true"
export NTP_STRATUM="8"

# 代理配置（如果有内网代理）
export HTTP_PROXY=""
export HTTPS_PROXY=""
export NO_PROXY="localhost,127.0.0.1,10.0.0.0/8,***********/16,**********/12"

# =============================================================================
# 离线安全配置
# =============================================================================

# 证书配置
export USE_SELF_SIGNED_CERTS="true"
export CERT_VALIDITY_DAYS="3650"
export CERT_COUNTRY="CN"
export CERT_STATE="Beijing"
export CERT_CITY="Beijing"
export CERT_ORG="Industrial Infrastructure"

# 密钥配置
export KEY_SIZE="2048"
export ENCRYPTION_ALGORITHM="AES256"

# =============================================================================
# 离线监控配置
# =============================================================================

# 监控数据保留
export PROMETHEUS_RETENTION="30d"  # 离线环境可能需要更长保留期
export GRAFANA_DATA_RETENTION="90d"

# 告警配置（离线环境）
export ALERT_EMAIL=""  # 离线环境可能无法发送邮件
export ALERT_WEBHOOK=""
export USE_LOCAL_ALERTING="true"

# =============================================================================
# 离线备份配置
# =============================================================================

# 备份策略（离线环境更重要）
export BACKUP_FREQUENCY="daily"
export BACKUP_RETENTION_DAYS="30"
export BACKUP_COMPRESSION="true"
export BACKUP_ENCRYPTION="true"

# 备份存储
export BACKUP_LOCAL_PATH="/backup/local"
export BACKUP_REMOTE_PATH=""  # 离线环境通常无远程备份
export USE_LOCAL_BACKUP_ONLY="true"

# =============================================================================
# 离线性能配置
# =============================================================================

# 缓存配置（离线环境优化）
export ENABLE_LOCAL_CACHE="true"
export CACHE_SIZE="2GB"
export CACHE_TTL="86400"  # 24小时

# 连接池配置
export CONNECTION_POOL_SIZE="50"
export CONNECTION_TIMEOUT="30"
export IDLE_TIMEOUT="300"

# =============================================================================
# 离线日志配置
# =============================================================================

# 日志级别（离线环境可能需要更详细的日志）
export LOG_LEVEL="INFO"
export DEBUG_MODE="false"
export VERBOSE_LOGGING="true"

# 日志保留
export LOG_RETENTION_DAYS="30"
export LOG_ROTATION_SIZE="100M"
export LOG_COMPRESSION="true"

# =============================================================================
# 离线验证函数
# =============================================================================

# 验证离线环境
validate_offline_environment() {
    local errors=0
    
    log_info "验证离线环境配置..."
    
    # 检查必需的离线资源
    local required_dirs=(
        "$LOCAL_YUM_REPO"
        "$LOCAL_PIP_REPO"
        "$OFFLINE_PACKAGE_DIR"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_error "离线资源目录不存在: $dir"
            errors=$((errors + 1))  # 自增1
        fi
    done
    
    # 检查关键软件包
    local mongodb_dir="$OFFLINE_PACKAGE_DIR/mongodb"
    if [[ ! -f "$mongodb_dir/$MONGODB_OFFLINE_PACKAGE" && ! -f "$mongodb_dir/$MONGODB_SOURCE_PACKAGE" ]]; then
        log_error "MongoDB安装包不存在"
        errors=$((errors + 1))
    fi
    
    local redis_dir="$OFFLINE_PACKAGE_DIR/redis"
    if [[ ! -f "$redis_dir/$REDIS_OFFLINE_PACKAGE" && ! -f "$redis_dir/$REDIS_SOURCE_PACKAGE" ]]; then
        log_error "Redis安装包不存在"
        errors=$((errors + 1))
    fi
    
    local kafka_dir="$OFFLINE_PACKAGE_DIR/kafka"
    if [[ ! -f "$kafka_dir/$KAFKA_PACKAGE" && ! -f "$kafka_dir/$KAFKA_ALT_PACKAGE" ]]; then
        log_error "Kafka安装包不存在"
        errors=$((errors + 1))
    fi
    
    local jdk_dir="$OFFLINE_PACKAGE_DIR/jdk"
    if [[ ! -f "$jdk_dir/$JDK_PACKAGE" && ! -f "$jdk_dir/$JDK_ALT_PACKAGE" ]]; then
        log_error "JDK安装包不存在"
        errors=$((errors + 1))
    fi
    
    if [[ $errors -gt 0 ]]; then
        log_error "离线环境验证失败，缺少必需的软件包"
        log_info "请运行 prepare_offline_packages.sh 准备离线安装包"
        return 1
    fi
    
    log_info "离线环境验证通过"
    return 0
}

# 显示离线环境信息
show_offline_info() {
    log_info "=== 离线部署环境信息 ==="
    log_info "离线模式: $OFFLINE_MODE"
    log_info "本地YUM仓库: $LOCAL_YUM_REPO"
    log_info "本地PIP仓库: $LOCAL_PIP_REPO"
    log_info "软件包目录: $OFFLINE_PACKAGE_DIR"
    log_info "使用预编译包: $([ "$MONGODB_USE_PRECOMPILED" == "true" ] && echo "是" || echo "否")"
    log_info "本地时钟同步: $USE_LOCAL_TIME"
    log_info "本地备份: $USE_LOCAL_BACKUP_ONLY"
    log_info "=========================="
}

# 切换到离线模式
switch_to_offline_mode() {
    log_info "切换到离线部署模式..."
    
    # 更新全局配置
    export OFFLINE_MODE="true"
    export INTERNET_ACCESS="false"
    export USE_LOCAL_REPO="true"
    
    # 验证离线环境
    if ! validate_offline_environment; then
        log_error "离线环境验证失败，无法切换到离线模式"
        return 1
    fi
    
    show_offline_info
    log_info "已切换到离线部署模式"
}

# =============================================================================
# 离线配置初始化
# =============================================================================

# 如果启用离线模式，自动验证环境
if [[ "$OFFLINE_MODE" == "true" ]]; then
    log_info "离线模式已启用"
    show_offline_info
    
    # 可选择是否在加载时验证（避免影响性能）
    if [[ "${VALIDATE_ON_LOAD:-false}" == "true" ]]; then
        validate_offline_environment
    fi
fi
