#!/bin/bash
# 网络配置脚本 - 工业场景基础服务自动部署
# 遵循DevOps最佳实践，自动化网络环境配置

set -euo pipefail

# =============================================================================
# 脚本初始化
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/lib/common.sh"

# 脚本信息
SCRIPT_NAME="网络配置脚本"
SCRIPT_VERSION="1.0.0"

log_info "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"

# =============================================================================
# 参数解析
# =============================================================================

# 默认参数
DRY_RUN=false
FORCE_REINSTALL=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force-reinstall)
            FORCE_REINSTALL=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项] [主机列表...]"
            echo "选项:"
            echo "  --force-reinstall   强制重新配置"
            echo "  --dry-run           仅显示将要执行的操作"
            echo "  -h, --help          显示此帮助信息"
            exit 0
            ;;
        -*)
            log_error "未知参数: $1"
            exit 1
            ;;
        *)
            # 剩余参数作为主机列表
            break
            ;;
    esac
done

# 获取主机列表
HOSTS=("$@")
if [[ ${#HOSTS[@]} -eq 0 ]]; then
    log_error "未指定主机列表"
    exit 1
fi

# =============================================================================
# 网络配置函数
# =============================================================================

configure_network_interface() {
    local host=$1
    
    log_info "在 $host 上配置网络接口..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上配置网络接口"
        return 0
    fi
    
    # 获取网络接口信息
    remote_execute "$host" "
        # 检查网络接口
        if ip link show $NETWORK_INTERFACE >/dev/null 2>&1; then
            log_info '网络接口 $NETWORK_INTERFACE 存在'
            
            # 优化网络接口参数
            ethtool -K $NETWORK_INTERFACE tso off gso off gro off lro off
            ethtool -G $NETWORK_INTERFACE rx 4096 tx 4096 || true
            
            log_info '网络接口优化完成'
        else
            log_warn '网络接口 $NETWORK_INTERFACE 不存在'
        fi
    "
    
    log_info "网络接口配置完成: $host"
}

configure_dns() {
    local host=$1
    
    log_info "在 $host 上配置DNS..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上配置DNS"
        return 0
    fi
    
    # 配置DNS服务器
    remote_execute "$host" "
        # 备份原始配置
        cp /etc/resolv.conf /etc/resolv.conf.backup
        
        # 配置DNS服务器
        cat > /etc/resolv.conf << 'EOF'
# DNS配置 - 工业场景优化
nameserver *******
nameserver ***************
nameserver *********
options timeout:2
options attempts:3
options rotate
EOF
        
        # 防止NetworkManager覆盖DNS配置
        chattr +i /etc/resolv.conf
        
        log_info 'DNS配置完成'
    "
    
    log_info "DNS配置完成: $host"
}

configure_hosts_file() {
    log_info "配置所有主机的hosts文件..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将配置所有主机的hosts文件"
        return 0
    fi
    
    # 生成hosts文件内容
    local hosts_content="# 工业场景基础服务主机映射
127.0.0.1   localhost localhost.localdomain
::1         localhost localhost.localdomain

# MongoDB集群
"
    
    local i=1
    for host in "${MONGODB_HOSTS[@]}"; do
        hosts_content+="$host   mongodb-$(printf "%02d" $i) mongo-$(printf "%02d" $i)
"
        i=$((i + 1))
    done
    
    hosts_content+="
# Redis集群
"
    i=1
    for host in "${REDIS_HOSTS[@]}"; do
        hosts_content+="$host   redis-$(printf "%02d" $i)
"
        i=$((i + 1))
    done
    
    hosts_content+="
# Kafka集群
"
    i=1
    for host in "${KAFKA_HOSTS[@]}"; do
        hosts_content+="$host   kafka-$(printf "%02d" $i)
"
        i=$((i + 1))
    done
    
    hosts_content+="
# TDEngine集群
"
    i=1
    for host in "${TDENGINE_HOSTS[@]}"; do
        hosts_content+="$host   tdengine-$(printf "%02d" $i)
"
        i=$((i + 1))
    done
    
    hosts_content+="
# NebulaGraph集群
"
    i=1
    for host in "${NEBULA_HOSTS[@]}"; do
        hosts_content+="$host   nebula-$(printf "%02d" $i)
"
        i=$((i + 1))
    done
    
    hosts_content+="
# 达梦数据库集群
"
    i=1
    for host in "${DAMENG_HOSTS[@]}"; do
        hosts_content+="$host   dameng-$(printf "%02d" $i) dm-$(printf "%02d" $i)
"
        i=$((i + 1))
    done
    
    hosts_content+="
# 监控服务器
"
    i=1
    for host in "${MONITOR_HOSTS[@]}"; do
        hosts_content+="$host   monitor-$(printf "%02d" $i)
"
        i=$((i + 1))
    done
    
    # 分发hosts文件到所有主机
    for host in "${HOSTS[@]}"; do
        log_info "更新 $host 的hosts文件"
        remote_execute "$host" "
            # 备份原始hosts文件
            cp /etc/hosts /etc/hosts.backup
            
            # 写入新的hosts文件
            cat > /etc/hosts << 'EOF'
$hosts_content
EOF
            
            log_info 'hosts文件更新完成'
        "
    done
    
    log_info "所有主机hosts文件配置完成"
}

configure_network_optimization() {
    local host=$1
    
    log_info "在 $host 上配置网络优化参数..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] 将在 $host 上配置网络优化参数"
        return 0
    fi
    
    remote_execute "$host" "
        # 网络优化参数已在系统配置中设置，这里进行额外优化
        
        # 配置网络队列
        echo 'net.core.netdev_budget = 600' >> /etc/sysctl.d/99-network-optimization.conf
        echo 'net.core.netdev_max_backlog = 5000' >> /etc/sysctl.d/99-network-optimization.conf
        
        # TCP优化
        echo 'net.ipv4.tcp_fin_timeout = 30' >> /etc/sysctl.d/99-network-optimization.conf
        echo 'net.ipv4.tcp_keepalive_time = 1200' >> /etc/sysctl.d/99-network-optimization.conf
        echo 'net.ipv4.tcp_keepalive_probes = 3' >> /etc/sysctl.d/99-network-optimization.conf
        echo 'net.ipv4.tcp_keepalive_intvl = 15' >> /etc/sysctl.d/99-network-optimization.conf
        
        # 应用配置
        sysctl -p /etc/sysctl.d/99-network-optimization.conf
        
        log_info '网络优化参数配置完成'
    "
    
    log_info "网络优化配置完成: $host"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_info "开始网络配置..."
    log_info "目标主机: ${HOSTS[*]}"
    
    # 配置hosts文件
    configure_hosts_file
    
    # 配置每个主机的网络
    for host in "${HOSTS[@]}"; do
        log_info "配置主机网络: $host"
        
        configure_network_interface "$host"
        configure_dns "$host"
        configure_network_optimization "$host"
        
        log_info "主机网络配置完成: $host"
    done
    
    # 验证网络连通性
    log_info "验证网络连通性..."
    for host in "${HOSTS[@]}"; do
        for target_host in "${HOSTS[@]}"; do
            if [[ "$host" != "$target_host" ]]; then
                if remote_execute "$host" "ping -c 3 -W 5 $target_host" >/dev/null 2>&1; then
                    log_debug "网络连通性正常: $host -> $target_host"
                else
                    log_warn "网络连通性异常: $host -> $target_host"
                fi
            fi
        done
    done
    
    log_info "网络配置完成"
}

# 执行主函数
main "$@"
