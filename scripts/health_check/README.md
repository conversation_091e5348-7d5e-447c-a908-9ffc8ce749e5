# 健康检查脚本集

本目录包含各基础服务的健康检查脚本，用于监控服务状态和及时发现问题。

## 脚本列表

### 通用脚本
- `check_all_services.sh` - 检查所有基础服务状态
- `check_system_resources.sh` - 检查系统资源使用情况
- `check_network_connectivity.sh` - 检查网络连通性

### 数据库服务
- `check_mongodb_cluster.sh` - MongoDB集群健康检查
- `check_redis_cluster.sh` - Redis集群健康检查
- `check_dameng_cluster.sh` - 达梦数据库集群检查

### 消息队列
- `check_kafka_cluster.sh` - Kafka集群健康检查

### 专业数据库
- `check_tdengine_cluster.sh` - TDEngine集群检查
- `check_nebula_cluster.sh` - NebulaGraph集群检查

## 使用方法

### 单独执行
```bash
# 检查MongoDB集群
./check_mongodb_cluster.sh

# 检查Redis集群
./check_redis_cluster.sh
```

### 定时执行
```bash
# 添加到crontab
*/5 * * * * /apps/scripts/health_check/check_all_services.sh
```

### 配置告警
脚本支持邮件告警，需要配置以下环境变量：
```bash
export ALERT_EMAIL="<EMAIL>"
export SMTP_SERVER="smtp.company.com"
```

## 脚本特性

- **自动发现**：自动发现集群节点
- **状态检查**：检查服务运行状态
- **性能监控**：监控关键性能指标
- **告警通知**：异常情况自动告警
- **日志记录**：详细的检查日志

## 配置文件

每个脚本都有对应的配置文件，位于 `config/` 目录下：
- `mongodb.conf` - MongoDB配置
- `redis.conf` - Redis配置
- `kafka.conf` - Kafka配置
- 等等

## 日志文件

检查日志默认保存在 `/var/log/health_check/` 目录下：
- `mongodb_health.log` - MongoDB检查日志
- `redis_health.log` - Redis检查日志
- `system_health.log` - 系统检查日志

## 注意事项

1. 确保脚本有执行权限
2. 配置正确的服务连接信息
3. 定期清理日志文件
4. 根据实际环境调整检查阈值

## 扩展开发

可以基于现有脚本模板开发新的健康检查脚本：
1. 复制模板脚本
2. 修改服务连接信息
3. 调整检查逻辑
4. 添加告警规则

---

**这些脚本是工业场景基础服务监控的重要组成部分，请根据实际需求进行配置和使用。**
