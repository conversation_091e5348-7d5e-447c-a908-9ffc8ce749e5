# Redis 工业场景高可用集群部署指南
## 银河麒麟+海光CPU（无AVX）环境

> **工业场景特化**：本指南专为工业环境设计，重点关注高可用性、数据持久化和系统稳定性，确保缓存服务的连续性和可靠性。

## 一、环境规划与要求

### 1.1 硬件配置要求

| 组件 | 最小配置 | 推荐配置 | 工业场景配置 |
|------|----------|----------|--------------|
| CPU | 2核心 | 4核心 | 8核心（海光CPU） |
| 内存 | 4GB | 16GB | 32GB+ |
| 存储 | 20GB HDD | 100GB SSD | 200GB+ NVMe SSD |
| 网络 | 百兆 | 千兆 | 万兆（集群间） |

### 1.2 系统环境要求
- **操作系统**：银河麒麟高级服务器版 V10 SP1/SP2
- **CPU架构**：海光（Hygon）系列，x86_64，不支持AVX指令集
- **文件系统**：推荐ext4或xfs，支持高并发I/O
- **网络端口**：7001-7006（集群端口）、16379（哨兵端口）

### 1.3 高可用架构设计

#### Redis集群架构（推荐）
```mermaid
graph TD
    A[应用层] --> B[负载均衡器]
    B --> C[Redis Master-1<br/>redis-01:7001]
    B --> D[Redis Master-2<br/>redis-02:7002]
    B --> E[Redis Master-3<br/>redis-03:7003]

    C -.->|复制| F[Redis Slave-1<br/>redis-04:7004]
    D -.->|复制| G[Redis Slave-2<br/>redis-05:7005]
    E -.->|复制| H[Redis Slave-3<br/>redis-06:7006]

    I[Sentinel-1] --> C
    I --> D
    I --> E
    J[Sentinel-2] --> C
    J --> D
    J --> E
    K[Sentinel-3] --> C
    K --> D
    K --> E
```

#### 服务器规划
| 主机名 | IP地址 | 角色 | 端口 | 配置 |
|--------|--------|------|------|------|
| redis-01 | ************* | Master-1 | 7001 | 16GB内存，200GB SSD |
| redis-02 | ************* | Master-2 | 7002 | 16GB内存，200GB SSD |
| redis-03 | ************* | Master-3 | 7003 | 16GB内存，200GB SSD |
| redis-04 | ************* | Slave-1 | 7004 | 16GB内存，200GB SSD |
| redis-05 | ************* | Slave-2 | 7005 | 16GB内存，200GB SSD |
| redis-06 | ************* | Slave-3 | 7006 | 16GB内存，200GB SSD |

## 二、依赖环境准备

### 2.1 系统依赖安装

#### 编译工具链
```bash
# 安装基础编译工具
yum groupinstall -y "Development Tools"
yum install -y gcc gcc-c++ make

# 安装系统库
yum install -y glibc-devel openssl-devel systemd-devel
yum install -y tcl tcl-devel jemalloc-devel
yum install -y numactl-devel libaio-devel

# 验证编译环境
gcc --version
make --version
```

### 2.2 用户和目录准备

#### 创建Redis用户
```bash
# 创建专用用户组和用户
groupadd -g 1301 redis
useradd -u 1301 -g redis -r -s /bin/false -d /var/lib/redis redis

# 创建必要目录
mkdir -p /apps/redis/{bin,conf,data,log,scripts}
mkdir -p /data/redis/{7001,7002,7003,7004,7005,7006}
mkdir -p /var/run/redis

# 设置目录权限
chown -R redis:redis /apps/redis /data/redis /var/run/redis
chmod 755 /apps/redis /data/redis
chmod 750 /var/run/redis
```

#### 系统参数优化
```bash
# 内核参数优化
cat >> /etc/sysctl.conf << 'EOF'
# Redis优化参数
vm.swappiness = 1
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.overcommit_memory = 1
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 5000
EOF

sysctl -p

# 禁用透明大页
echo never > /sys/kernel/mm/transparent_hugepage/enabled
echo never > /sys/kernel/mm/transparent_hugepage/defrag

# 永久禁用透明大页
cat >> /etc/rc.local << 'EOF'
echo never > /sys/kernel/mm/transparent_hugepage/enabled
echo never > /sys/kernel/mm/transparent_hugepage/defrag
EOF
chmod +x /etc/rc.local
```

## 三、Redis源码编译安装

### 3.1 获取源码

#### 下载Redis源码
```bash
# 下载Redis 7.0.8源码（推荐版本）
cd /apps/software
wget https://download.redis.io/releases/redis-7.0.8.tar.gz
tar -zxvf redis-7.0.8.tar.gz
cd redis-7.0.8

# 如果网络受限，可从镜像站下载
# wget https://mirrors.tuna.tsinghua.edu.cn/redis/redis-7.0.8.tar.gz
```

### 3.2 编译配置

#### 针对海光CPU优化编译
```bash
# 设置编译环境变量
export CC=gcc
export CXX=g++
export CFLAGS="-O2 -march=x86-64 -mtune=generic"
export CXXFLAGS="-O2 -march=x86-64 -mtune=generic"

# 针对海光CPU优化，禁用AVX
export PORTABLE=1
export USE_SYSTEMD=yes

# 编译Redis
make MALLOC=jemalloc \
     BUILD_WITH_SYSTEMD=yes \
     PREFIX=/apps/redis \
     install

# 验证编译结果
/apps/redis/bin/redis-server --version
```

#### 创建配置目录结构
```bash
# 创建配置文件目录
mkdir -p /apps/redis/conf
mkdir -p /apps/redis/scripts

# 复制配置文件模板
cp redis.conf /apps/redis/conf/redis-template.conf
cp sentinel.conf /apps/redis/conf/sentinel-template.conf

# 设置环境变量
echo 'export REDIS_HOME=/apps/redis' >> /etc/profile
echo 'export PATH=$PATH:$REDIS_HOME/bin' >> /etc/profile
source /etc/profile
```

## 四、Redis集群配置

### 4.1 Master节点配置

#### Master-1配置（redis-01:7001）
```bash
# /apps/redis/conf/redis-7001.conf
port 7001
bind 0.0.0.0
protected-mode no
daemonize yes
pidfile /var/run/redis/redis-7001.pid
logfile /apps/redis/log/redis-7001.log
loglevel notice

# 数据目录
dir /data/redis/7001
dbfilename dump-7001.rdb

# 持久化配置（工业场景优化）
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes

# AOF持久化
appendonly yes
appendfilename "appendonly-7001.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# 集群配置
cluster-enabled yes
cluster-config-file nodes-7001.conf
cluster-node-timeout 15000
cluster-announce-ip *************
cluster-announce-port 7001
cluster-announce-bus-port 17001
cluster-require-full-coverage no

# 内存配置
maxmemory 8gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 网络优化
tcp-keepalive 300
timeout 0
tcp-backlog 511
maxclients 10000

# 安全配置
requirepass "your_strong_password_here"
masterauth "your_strong_password_here"

# 工业场景优化
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 慢日志配置
slowlog-log-slower-than 10000
slowlog-max-len 128

# 监控配置
latency-monitor-threshold 100
```

#### 其他Master节点配置
```bash
# redis-02:7002 配置差异
port 7002
cluster-announce-ip *************
cluster-announce-port 7002
cluster-announce-bus-port 17002
dir /data/redis/7002
dbfilename dump-7002.rdb
appendfilename "appendonly-7002.aof"
pidfile /var/run/redis/redis-7002.pid
logfile /apps/redis/log/redis-7002.log
cluster-config-file nodes-7002.conf

# redis-03:7003 配置差异
port 7003
cluster-announce-ip *************
cluster-announce-port 7003
cluster-announce-bus-port 17003
dir /data/redis/7003
dbfilename dump-7003.rdb
appendfilename "appendonly-7003.aof"
pidfile /var/run/redis/redis-7003.pid
logfile /apps/redis/log/redis-7003.log
cluster-config-file nodes-7003.conf
```

### 4.2 Slave节点配置

#### Slave节点配置模板
```bash
# redis-04:7004 (Slave of redis-01:7001)
port 7004
bind 0.0.0.0
protected-mode no
daemonize yes
pidfile /var/run/redis/redis-7004.pid
logfile /apps/redis/log/redis-7004.log
loglevel notice

# 数据目录
dir /data/redis/7004
dbfilename dump-7004.rdb

# 持久化配置
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfilename "appendonly-7004.aof"
appendfsync everysec

# 集群配置
cluster-enabled yes
cluster-config-file nodes-7004.conf
cluster-node-timeout 15000
cluster-announce-ip *************
cluster-announce-port 7004
cluster-announce-bus-port 17004
cluster-require-full-coverage no

# 内存配置
maxmemory 8gb
maxmemory-policy allkeys-lru

# 安全配置
requirepass "your_strong_password_here"
masterauth "your_strong_password_here"

# 从节点优化配置
replica-read-only yes
replica-serve-stale-data yes
replica-priority 100
```

### 4.3 systemd服务配置

#### 创建服务模板
```bash
# 创建Redis集群启动脚本
cat > /apps/redis/scripts/redis-cluster-service.sh << 'EOF'
#!/bin/bash

REDIS_PORTS=(7001 7002 7003 7004 7005 7006)
REDIS_BIN="/apps/redis/bin/redis-server"
ACTION=$1

start_redis() {
    local port=$1
    echo "启动Redis节点: $port"
    sudo -u redis $REDIS_BIN /apps/redis/conf/redis-$port.conf
    sleep 2
}

stop_redis() {
    local port=$1
    echo "停止Redis节点: $port"
    sudo -u redis /apps/redis/bin/redis-cli -p $port -a "your_strong_password_here" shutdown
    sleep 1
}

case "$ACTION" in
    start)
        for port in "${REDIS_PORTS[@]}"; do
            start_redis $port
        done
        ;;
    stop)
        for port in "${REDIS_PORTS[@]}"; do
            stop_redis $port
        done
        ;;
    restart)
        $0 stop
        sleep 5
        $0 start
        ;;
    status)
        for port in "${REDIS_PORTS[@]}"; do
            if pgrep -f "redis-server.*:$port" > /dev/null; then
                echo "Redis $port: 运行中"
            else
                echo "Redis $port: 已停止"
            fi
        done
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac
EOF

chmod +x /apps/redis/scripts/redis-cluster-service.sh
```

#### 创建systemd服务文件
```bash
# 为每个Redis实例创建服务文件
for port in 7001 7002 7003 7004 7005 7006; do
cat > /etc/systemd/system/redis-$port.service << EOF
[Unit]
Description=Redis Server $port
After=network.target

[Service]
Type=forking
User=redis
Group=redis
PIDFile=/var/run/redis/redis-$port.pid
ExecStart=/apps/redis/bin/redis-server /apps/redis/conf/redis-$port.conf
ExecReload=/bin/kill -HUP \$MAINPID
ExecStop=/apps/redis/bin/redis-cli -p $port -a "your_strong_password_here" shutdown
Restart=always
RestartSec=10
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target
EOF
done

# 重载systemd配置
systemctl daemon-reload

# 启用所有Redis服务
for port in 7001 7002 7003 7004 7005 7006; do
    systemctl enable redis-$port
done
```

## 五、集群部署与初始化

### 5.1 启动所有节点

#### 启动Redis实例
```bash
# 启动所有Redis节点
for port in 7001 7002 7003 7004 7005 7006; do
    systemctl start redis-$port
    echo "启动Redis $port"
    sleep 2
done

# 验证启动状态
for port in 7001 7002 7003 7004 7005 7006; do
    systemctl status redis-$port
done

# 检查端口监听
netstat -tlnp | grep -E "700[1-6]"
```

#### 验证节点连接
```bash
# 测试每个节点连接
for port in 7001 7002 7003 7004 7005 7006; do
    echo "测试Redis $port 连接:"
    /apps/redis/bin/redis-cli -p $port -a "your_strong_password_here" ping
done
```

### 5.2 创建Redis集群

#### 初始化集群
```bash
# 创建Redis集群（3主3从）
/apps/redis/bin/redis-cli --cluster create \
  *************:7001 *************:7002 *************:7003 \
  *************:7004 *************:7005 *************:7006 \
  --cluster-replicas 1 \
  -a "your_strong_password_here"

# 确认集群创建
# 输入 yes 确认slot分配
```

#### 验证集群状态
```bash
# 检查集群节点信息
/apps/redis/bin/redis-cli -c -h ************* -p 7001 -a "your_strong_password_here" cluster nodes

# 检查集群信息
/apps/redis/bin/redis-cli -c -h ************* -p 7001 -a "your_strong_password_here" cluster info

# 检查slot分配
/apps/redis/bin/redis-cli -c -h ************* -p 7001 -a "your_strong_password_here" cluster slots
```

### 5.3 集群测试验证

#### 数据读写测试
```bash
# 连接集群进行测试
/apps/redis/bin/redis-cli -c -h ************* -p 7001 -a "your_strong_password_here"

# 在redis-cli中执行测试
127.0.0.1:7001> set test:key1 "value1"
127.0.0.1:7001> set test:key2 "value2"
127.0.0.1:7001> set test:key3 "value3"

# 验证数据分布
127.0.0.1:7001> get test:key1
127.0.0.1:7001> get test:key2
127.0.0.1:7001> get test:key3

# 查看key分布在哪个slot
127.0.0.1:7001> cluster keyslot test:key1
127.0.0.1:7001> cluster keyslot test:key2
127.0.0.1:7001> cluster keyslot test:key3
```

#### 故障转移测试
```bash
# 模拟主节点故障
systemctl stop redis-7001

# 观察集群状态变化
/apps/redis/bin/redis-cli -c -h ************* -p 7002 -a "your_strong_password_here" cluster nodes

# 验证数据仍然可访问
/apps/redis/bin/redis-cli -c -h ************* -p 7002 -a "your_strong_password_here" get test:key1

# 恢复主节点
systemctl start redis-7001

# 观察节点重新加入集群
/apps/redis/bin/redis-cli -c -h ************* -p 7001 -a "your_strong_password_here" cluster nodes
```

## 六、监控与运维管理

### 6.1 健康检查脚本

#### 集群状态监控
```bash
#!/bin/bash
# /apps/redis/scripts/health_check.sh

REDIS_NODES=("*************:7001" "*************:7002" "*************:7003"
             "*************:7004" "*************:7005" "*************:7006")
LOG_FILE="/var/log/redis_health.log"
REDIS_CLI="/apps/redis/bin/redis-cli"
PASSWORD="your_strong_password_here"

check_redis_node() {
    local node=$1
    local host=${node%:*}
    local port=${node#*:}
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    echo "[$timestamp] 检查Redis节点 $node" >> $LOG_FILE

    # 检查节点连通性
    if ! timeout 5 $REDIS_CLI -h $host -p $port -a $PASSWORD ping >/dev/null 2>&1; then
        echo "[$timestamp] 严重告警 - Redis节点 $node 不可达" >> $LOG_FILE
        send_alert "Redis节点故障" "节点 $node 无法连接"
        return 1
    fi

    # 检查集群状态
    local cluster_state=$($REDIS_CLI -h $host -p $port -a $PASSWORD cluster info 2>/dev/null | grep "cluster_state" | cut -d: -f2)
    if [ "$cluster_state" != "ok" ]; then
        echo "[$timestamp] 警告 - Redis集群状态异常: $cluster_state" >> $LOG_FILE
        send_alert "Redis集群状态异常" "集群状态: $cluster_state"
    fi

    # 检查内存使用率
    local memory_info=$($REDIS_CLI -h $host -p $port -a $PASSWORD info memory 2>/dev/null)
    local used_memory=$(echo "$memory_info" | grep "used_memory:" | cut -d: -f2 | tr -d '\r')
    local maxmemory=$(echo "$memory_info" | grep "maxmemory:" | cut -d: -f2 | tr -d '\r')

    if [ "$maxmemory" -gt 0 ]; then
        local memory_usage=$((used_memory * 100 / maxmemory))
        if [ "$memory_usage" -gt 80 ]; then
            echo "[$timestamp] 警告 - Redis节点 $node 内存使用率: ${memory_usage}%" >> $LOG_FILE
            send_alert "Redis内存告警" "节点 $node 内存使用率: ${memory_usage}%"
        fi
    fi

    # 检查连接数
    local connected_clients=$($REDIS_CLI -h $host -p $port -a $PASSWORD info clients 2>/dev/null | grep "connected_clients:" | cut -d: -f2 | tr -d '\r')
    if [ "$connected_clients" -gt 8000 ]; then
        echo "[$timestamp] 警告 - Redis节点 $node 连接数过多: $connected_clients" >> $LOG_FILE
        send_alert "Redis连接数告警" "节点 $node 连接数: $connected_clients"
    fi
}

send_alert() {
    local subject=$1
    local message=$2
    echo "$message" | mail -s "$subject" <EMAIL>
}

# 检查所有Redis节点
for node in "${REDIS_NODES[@]}"; do
    check_redis_node $node
done

# 检查集群整体状态
cluster_size=$($REDIS_CLI -h ************* -p 7001 -a $PASSWORD cluster nodes 2>/dev/null | grep "master" | wc -l)
if [ "$cluster_size" -ne 3 ]; then
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] 严重告警 - Redis集群主节点数量异常: $cluster_size" >> $LOG_FILE
    send_alert "Redis集群主节点异常" "当前主节点数量: $cluster_size"
fi
```

#### 性能监控脚本
```bash
#!/bin/bash
# /apps/redis/scripts/performance_monitor.sh

REDIS_HOST="*************"
REDIS_PORT="7001"
METRICS_FILE="/var/log/redis_metrics.log"
REDIS_CLI="/apps/redis/bin/redis-cli"
PASSWORD="your_strong_password_here"

collect_metrics() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 收集基本信息
    local info=$($REDIS_CLI -h $REDIS_HOST -p $REDIS_PORT -a $PASSWORD info all 2>/dev/null)

    # 提取关键指标
    local connected_clients=$(echo "$info" | grep "connected_clients:" | cut -d: -f2 | tr -d '\r')
    local used_memory=$(echo "$info" | grep "used_memory_human:" | cut -d: -f2 | tr -d '\r')
    local ops_per_sec=$(echo "$info" | grep "instantaneous_ops_per_sec:" | cut -d: -f2 | tr -d '\r')
    local keyspace_hits=$(echo "$info" | grep "keyspace_hits:" | cut -d: -f2 | tr -d '\r')
    local keyspace_misses=$(echo "$info" | grep "keyspace_misses:" | cut -d: -f2 | tr -d '\r')

    # 计算命中率
    if [ "$keyspace_hits" -gt 0 ] || [ "$keyspace_misses" -gt 0 ]; then
        local hit_rate=$((keyspace_hits * 100 / (keyspace_hits + keyspace_misses)))
    else
        local hit_rate=0
    fi

    # 记录指标
    echo "[$timestamp] 连接数: $connected_clients, 内存使用: $used_memory, QPS: $ops_per_sec, 命中率: ${hit_rate}%" >> $METRICS_FILE

    # 收集慢日志
    local slow_logs=$($REDIS_CLI -h $REDIS_HOST -p $REDIS_PORT -a $PASSWORD slowlog get 5 2>/dev/null)
    if [ -n "$slow_logs" ]; then
        echo "[$timestamp] 慢日志:" >> $METRICS_FILE
        echo "$slow_logs" >> $METRICS_FILE
    fi
}

collect_metrics

# 清理旧日志（保留7天）
find /var/log -name "redis_*.log" -mtime +7 -delete
```

### 6.2 备份恢复策略

#### 数据备份脚本
```bash
#!/bin/bash
# /apps/redis/scripts/backup_redis.sh

BACKUP_DIR="/backup/redis"
DATE=$(date +%Y%m%d_%H%M%S)
REDIS_NODES=("*************:7001" "*************:7002" "*************:7003")
REDIS_CLI="/apps/redis/bin/redis-cli"
PASSWORD="your_strong_password_here"

# 创建备份目录
mkdir -p $BACKUP_DIR/$DATE

echo "$(date): 开始Redis集群备份"

# 备份每个主节点
for node in "${REDIS_NODES[@]}"; do
    local host=${node%:*}
    local port=${node#*:}

    echo "$(date): 备份节点 $node"

    # 执行BGSAVE
    $REDIS_CLI -h $host -p $port -a $PASSWORD bgsave

    # 等待备份完成
    while [ "$($REDIS_CLI -h $host -p $port -a $PASSWORD lastsave)" = "$($REDIS_CLI -h $host -p $port -a $PASSWORD lastsave)" ]; do
        sleep 1
    done

    # 复制RDB文件
    scp $host:/data/redis/$port/dump-$port.rdb $BACKUP_DIR/$DATE/

    # 复制AOF文件
    scp $host:/data/redis/$port/appendonly-$port.aof $BACKUP_DIR/$DATE/

    # 复制配置文件
    scp $host:/apps/redis/conf/redis-$port.conf $BACKUP_DIR/$DATE/
done

# 压缩备份文件
cd $BACKUP_DIR
tar -czf "redis_backup_$DATE.tar.gz" "$DATE"
rm -rf "$DATE"

echo "$(date): Redis集群备份完成: redis_backup_$DATE.tar.gz"

# 清理过期备份（保留7天）
find $BACKUP_DIR -name "redis_backup_*.tar.gz" -mtime +7 -delete
```

## 七、常见问题与故障排查

### 7.1 编译安装问题

#### 编译依赖问题
```bash
# 错误信息：fatal error: jemalloc/jemalloc.h: No such file or directory
# 解决方案：
yum install -y jemalloc-devel

# 或者使用系统默认内存分配器
make MALLOC=libc install
```

#### 海光CPU兼容性问题
```bash
# 错误信息：Illegal instruction
# 解决方案：
1. 确认编译时使用了正确的CFLAGS
export CFLAGS="-O2 -march=x86-64 -mtune=generic"

2. 禁用AVX指令
export PORTABLE=1

3. 重新编译
make clean && make MALLOC=jemalloc install
```

### 7.2 集群运行问题

#### 集群初始化失败
```bash
# 错误信息：[ERR] Node xxx is not empty
# 解决方案：
1. 清理节点数据
/apps/redis/bin/redis-cli -p 7001 -a password flushall
/apps/redis/bin/redis-cli -p 7001 -a password cluster reset

2. 删除集群配置文件
rm -f /data/redis/*/nodes-*.conf

3. 重新初始化集群
```

#### 节点故障转移问题
```bash
# 检查集群状态
/apps/redis/bin/redis-cli -c -h ************* -p 7001 -a password cluster nodes

# 手动故障转移
/apps/redis/bin/redis-cli -c -h ************* -p 7004 -a password cluster failover

# 修复故障节点
systemctl restart redis-7001
/apps/redis/bin/redis-cli -c -h ************* -p 7001 -a password cluster meet ************* 7002
```

### 7.3 性能优化

#### 内存优化
```bash
# 检查内存使用
/apps/redis/bin/redis-cli -h ************* -p 7001 -a password info memory

# 优化内存策略
/apps/redis/bin/redis-cli -h ************* -p 7001 -a password config set maxmemory-policy allkeys-lru

# 检查大key
/apps/redis/bin/redis-cli -h ************* -p 7001 -a password --bigkeys
```

#### 网络优化
```bash
# 调整网络参数
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
sysctl -p

# 优化Redis网络配置
/apps/redis/bin/redis-cli -h ************* -p 7001 -a password config set tcp-backlog 511
/apps/redis/bin/redis-cli -h ************* -p 7001 -a password config set timeout 0
```

---

## 八、参考资料与最佳实践

### 8.1 官方文档
- [Redis官方文档](https://redis.io/docs/)
- [Redis集群教程](https://redis.io/docs/manual/scaling/)
- [Redis配置参数](https://redis.io/docs/manual/config/)

### 8.2 工业场景最佳实践
- **数据安全**：启用AOF持久化，定期备份，多副本保护
- **高可用性**：集群模式部署，自动故障转移，监控告警
- **性能优化**：合理内存配置，网络优化，慢查询监控
- **运维管理**：自动化脚本，标准化流程，容量规划

---

**本指南将根据实际部署经验和Redis版本更新持续完善，确保在工业场景下的最佳实践。**