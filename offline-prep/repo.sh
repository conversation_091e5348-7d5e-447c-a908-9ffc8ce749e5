#!/bin/bash

# 目标目录
REPO_DIR="/opt/offline-prep/yum-repo"

# 软件包列表
PACKAGES=(
    wget curl rsync unzip tar gzip
    git vim htop iotop nethogs
    net-tools telnet nc tcpdump
    lsof strace sysstat dstat
    chrony logrotate crontabs
    cmake pkgconfig openssl-devel zlib-devel
    libcurl-devel readline-devel ncurses-devel
    libaio-devel numactl-devel
    jemalloc-devel python3 python3-pip python3-devel
)

cd $REPO_DIR

# 3. 下载软件包及依赖
for pkg in "${PACKAGES[@]}"; do
    sudo yumdownloader --resolve --destdir=$REPO_DIR $pkg
done

yum groupinstall "Development Tools" --downloadonly --downloaddir=$REPO_DIR

# 4. 清理可能的重复RPM（可选）
sudo rm -f *.i?86.rpm  # 移除32位包（如果存在）
rpm -Uvh createrepo_c-1.0.1-1.oe2403.x86_64.rpm --nodeps --force
rpm -Uvh drpm-0.5.2-1.oe2403.x86_64.rpm --nodeps --force
# 5. 创建仓库元数据
sudo createrepo --database $REPO_DIR

# 6. 创建仓库配置文件
sudo tee /etc/yum.repos.d/offline.repo <<EOF
[offline-repo]
name=Offline Local Repository
baseurl=file://$REPO_DIR
enabled=1
gpgcheck=0
priority=1
EOF

# 7. 清理YUM缓存
sudo yum clean all
sudo rm -rf /var/cache/yum

echo "本地Yum仓库创建完成！"
echo "仓库位置: $REPO_DIR"
echo "已添加仓库配置: /etc/yum.repos.d/offline.repo"
