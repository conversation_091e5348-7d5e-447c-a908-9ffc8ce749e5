# 达梦数据库 工业场景高可用部署指南
## 银河麒麟+海光CPU环境

> **工业场景特化**：本指南专为工业环境设计，重点关注数据安全、事务一致性和系统稳定性，满足关键业务系统的高可用要求。

## 一、环境规划与要求

### 1.1 硬件配置要求

| 组件 | 最小配置 | 推荐配置 | 工业场景配置 |
|------|----------|----------|--------------|
| CPU | 4核心 | 8核心 | 16核心（海光CPU） |
| 内存 | 8GB | 32GB | 64GB+ |
| 存储 | 100GB HDD | 500GB SSD | 1TB+ NVMe SSD |
| 网络 | 百兆 | 千兆 | 万兆（集群间） |

### 1.2 系统环境要求
- **操作系统**：银河麒麟高级服务器版 V10 SP1/SP2
- **CPU架构**：海光（Hygon）系列，x86_64
- **文件系统**：推荐ext4或xfs
- **网络端口**：5236（默认端口）、5237（监听端口）

### 1.3 高可用架构设计

#### 主从复制架构
```mermaid
graph TD
    A[应用层] --> B[负载均衡器]
    B --> C[Primary DB<br/>dm-01:5236]
    B --> D[Standby DB<br/>dm-02:5236]
    B --> E[Standby DB<br/>dm-03:5236]
    
    C -.->|实时复制| D
    C -.->|实时复制| E
    D -.->|心跳检测| C
    E -.->|心跳检测| C
    
    F[监控系统] --> C
    F --> D
    F --> E
```

#### 服务器规划
| 主机名 | IP地址 | 角色 | 配置 | 说明 |
|--------|--------|------|------|------|
| dm-01 | ************* | Primary | 64GB内存，1TB SSD | 主库，处理读写 |
| dm-02 | ************* | Standby | 64GB内存，1TB SSD | 备库，只读查询 |
| dm-03 | ************* | Standby | 64GB内存，1TB SSD | 备库，灾备 |

## 二、安装包获取与准备

### 2.1 获取安装包

#### 官方渠道
- **官方网站**：https://www.dameng.com/download.html
- **技术支持**：联系达梦技术支持获取银河麒麟适配版
- **合作伙伴**：通过授权合作伙伴获取企业版

#### 推荐版本
- **达梦数据库V8**：银河麒麟认证版本
- **版本号**：DM8 V8.1.2.128 或更高版本
- **架构支持**：x86_64（海光CPU优化版）

### 2.2 依赖环境准备

#### 系统依赖安装
```bash
# 安装基础依赖
yum install -y libaio libaio-devel
yum install -y libncurses5 libncurses5-dev
yum install -y compat-libstdc++-33
yum install -y unixODBC unixODBC-devel
yum install -y readline-devel

# 安装字符集支持
yum install -y glibc-locale-source glibc-langpack-zh

# 创建达梦用户
groupadd -g 1001 dmdba
useradd -u 1001 -g dmdba -r -s /bin/bash -d /home/<USER>
echo "dmdba:dm123456" | chpasswd
```

#### 系统参数优化
```bash
# 内核参数优化
cat >> /etc/sysctl.conf << 'EOF'
# 达梦数据库优化参数
kernel.shmmax = 68719476736
kernel.shmall = 4294967296
kernel.shmmni = 4096
kernel.sem = 250 32000 100 128
fs.file-max = 6815744
net.ipv4.ip_local_port_range = 9000 65500
net.core.rmem_default = 262144
net.core.rmem_max = 4194304
net.core.wmem_default = 262144
net.core.wmem_max = 1048576
EOF

sysctl -p

# 用户限制优化
cat >> /etc/security/limits.conf << 'EOF'
dmdba soft nofile 65536
dmdba hard nofile 65536
dmdba soft nproc 16384
dmdba hard nproc 16384
dmdba soft stack 32768
dmdba hard stack 32768
EOF
```

## 三、达梦数据库安装

### 3.1 安装主库（dm-01）

#### 解压安装包
```bash
# 切换到dmdba用户
su - dmdba

# 解压安装包
cd /apps/software
tar -zxvf dm8_20220822_x86_rh6_64.tar

# 进入安装目录
cd dm8_20220822_x86_rh6_64
```

#### 执行安装
```bash
# 图形界面安装（如果有X11转发）
./DMInstall.bin

# 命令行安装
./DMInstall.bin -i

# 安装参数选择：
# 安装路径：/apps/dmdbms
# 安装类型：服务器
# 确认安装
```

#### 创建数据库实例
```bash
# 切换到安装目录
cd /apps/dmdbms/bin

# 创建数据库实例
./dminit DAMENG_PATH=/apps/dmdbms/data \
         DB_NAME=DMSERVER \
         INSTANCE_NAME=DMSERVER \
         PORT_NUM=5236 \
         PAGE_SIZE=32 \
         EXTENT_SIZE=32 \
         CASE_SENSITIVE=Y \
         CHARSET=1 \
         LENGTH_IN_CHAR=Y \
         LOG_SIZE=1024 \
         BUFFER=1000

# 参数说明：
# DAMENG_PATH: 数据库路径
# DB_NAME: 数据库名称
# PORT_NUM: 监听端口
# PAGE_SIZE: 页面大小(KB)
# CHARSET: 字符集(1=UTF-8)
# LOG_SIZE: 日志文件大小(MB)
# BUFFER: 缓冲区大小(MB)
```

### 3.2 配置文件优化

#### 主库配置（dm.ini）
```ini
# /apps/dmdbms/data/DMSERVER/dm.ini

# 基本配置
INSTANCE_NAME = DMSERVER
PORT_NUM = 5236
DBA_PWD_POLICY = 0
PWD_POLICY = 0

# 内存配置
BUFFER = 4000          # 缓冲区大小(MB)
SORT_BUF_SIZE = 100    # 排序缓冲区大小(MB)
HASH_BUF_SIZE = 100    # 哈希缓冲区大小(MB)

# 日志配置
RLOG_SEND_APPLY_MON = 64    # 日志发送监控
RLOG_APPLY_MON = 64         # 日志应用监控
ARCH_INI = 1                # 启用归档
ARCH_DEST = /apps/dmdbms/arch # 归档目录
ARCH_INCOMING_PATH = /apps/dmdbms/arch

# 连接配置
MAX_SESSIONS = 2000         # 最大会话数
MAX_WORKER_THREADS = 200    # 最大工作线程数

# 工业场景优化
CKPT_INTV = 300            # 检查点间隔(秒)
FAST_COMMIT = 1            # 快速提交
ENABLE_MONITOR = 1         # 启用监控
MONITOR_TIME = 60          # 监控间隔(秒)

# 高可用配置
MAL_INI = 1                # 启用主备
ALTER_MODE_STATUS = 0      # 主库模式
ARCH_WAIT_APPLY = 1        # 归档等待应用
```

### 3.3 启动数据库服务

#### 创建systemd服务
```bash
# 创建服务文件
sudo cat > /etc/systemd/system/dmserver.service << 'EOF'
[Unit]
Description=Dameng Database Server
After=network.target

[Service]
Type=forking
User=dmdba
Group=dmdba
ExecStart=/apps/dmdbms/bin/dmserver /apps/dmdbms/data/DMSERVER/dm.ini
ExecStop=/apps/dmdbms/bin/dmserver /apps/dmdbms/data/DMSERVER/dm.ini stop
Restart=always
RestartSec=10
LimitNOFILE=65536
LimitNPROC=16384

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
sudo systemctl daemon-reload
sudo systemctl enable dmserver
sudo systemctl start dmserver
```

#### 验证安装
```bash
# 检查服务状态
systemctl status dmserver

# 检查端口监听
netstat -tlnp | grep 5236

# 连接数据库测试
/apps/dmdbms/bin/disql SYSDBA/SYSDBA@localhost:5236

# 在disql中执行测试
SQL> SELECT * FROM V$VERSION;
SQL> SELECT INSTANCE_NAME FROM V$INSTANCE;
```

## 四、高可用集群配置

### 4.1 配置主从复制

#### 备库安装（dm-02, dm-03）
```bash
# 在备库服务器上执行相同的安装步骤
# 但在创建实例时设置为备库模式

./dminit DAMENG_PATH=/apps/dmdbms/data \
         DB_NAME=DMSERVER \
         INSTANCE_NAME=DMSERVER \
         PORT_NUM=5236 \
         PAGE_SIZE=32 \
         EXTENT_SIZE=32 \
         CASE_SENSITIVE=Y \
         CHARSET=1 \
         LENGTH_IN_CHAR=Y \
         LOG_SIZE=1024 \
         BUFFER=1000 \
         ARCH_INI=1 \
         MAL_INI=1
```

#### 备库配置（dm.ini）
```ini
# 备库特殊配置
ALTER_MODE_STATUS = 1      # 备库模式
MAL_CHECK_INTERVAL = 5     # 主备检查间隔
MAL_CONN_FAIL_INTERVAL = 5 # 连接失败间隔
RLOG_SEND_THRESHOLD = 0    # 日志发送阈值

# 主库连接信息
MAL_INST_HOST = *************
MAL_INST_PORT = 5236
MAL_INST_NAME = DMSERVER
```

#### 配置主从复制
```sql
-- 在主库执行
-- 1. 创建复制用户
CREATE USER repl_user IDENTIFIED BY "repl_password";
GRANT DBA TO repl_user;

-- 2. 启用归档模式
ALTER DATABASE ARCHIVELOG;

-- 3. 配置主备参数
ALTER SYSTEM SET MAL_INI = 1;
ALTER SYSTEM SET ARCH_INI = 1;

-- 在备库执行
-- 1. 配置主库连接
ALTER SYSTEM SET MAL_INST_HOST = '*************';
ALTER SYSTEM SET MAL_INST_PORT = 5236;
ALTER SYSTEM SET MAL_INST_NAME = 'DMSERVER';

-- 2. 启动主备同步
ALTER DATABASE MOUNT;
ALTER DATABASE OPEN;
```

### 4.2 读写分离配置

#### 应用连接配置
```bash
# 主库连接（写操作）
PRIMARY_URL="dm://*************:5236/DMSERVER"

# 备库连接（读操作）
READONLY_URL="dm://*************:5236/DMSERVER,*************:5236/DMSERVER"

# 负载均衡连接字符串
CLUSTER_URL="dm://*************:5236,*************:5236,*************:5236/DMSERVER?loginMode=primary_preferred"
```

## 五、监控与运维管理

### 5.1 健康检查脚本

#### 数据库状态监控
```bash
#!/bin/bash
# /apps/dmdbms/scripts/health_check.sh

DM_HOSTS=("*************:5236" "*************:5236" "*************:5236")
LOG_FILE="/var/log/dm_health.log"
DISQL_PATH="/apps/dmdbms/bin/disql"

check_db_status() {
    local host=$1
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    echo "[$timestamp] 检查数据库 $host" >> $LOG_FILE

    # 检查数据库连接
    if ! timeout 10 $DISQL_PATH SYSDBA/SYSDBA@$host -e "SELECT 1 FROM DUAL;" >/dev/null 2>&1; then
        echo "[$timestamp] 严重告警 - 数据库 $host 无法连接" >> $LOG_FILE
        send_alert "达梦数据库连接失败" "数据库 $host 无法连接"
        return 1
    fi

    # 检查数据库状态
    local status=$($DISQL_PATH SYSDBA/SYSDBA@$host -e "SELECT STATUS FROM V\$INSTANCE;" 2>/dev/null | grep -v "STATUS" | grep -v "^$" | head -1)
    echo "[$timestamp] 数据库 $host 状态: $status" >> $LOG_FILE

    if [ "$status" != "OPEN" ]; then
        echo "[$timestamp] 警告 - 数据库 $host 状态异常: $status" >> $LOG_FILE
        send_alert "达梦数据库状态异常" "数据库 $host 状态: $status"
    fi

    # 检查表空间使用率
    local tablespace_usage=$($DISQL_PATH SYSDBA/SYSDBA@$host -e "
    SELECT TABLESPACE_NAME,
           ROUND((USED_SIZE/TOTAL_SIZE)*100, 2) AS USAGE_PCT
    FROM V\$TABLESPACE
    WHERE ROUND((USED_SIZE/TOTAL_SIZE)*100, 2) > 80;
    " 2>/dev/null | grep -v "TABLESPACE_NAME" | grep -v "^$")

    if [ -n "$tablespace_usage" ]; then
        echo "[$timestamp] 警告 - 表空间使用率过高: $tablespace_usage" >> $LOG_FILE
        send_alert "达梦数据库表空间告警" "表空间使用率过高: $tablespace_usage"
    fi
}

send_alert() {
    local subject=$1
    local message=$2
    echo "$message" | mail -s "$subject" <EMAIL>
}

# 检查所有数据库实例
for host in "${DM_HOSTS[@]}"; do
    check_db_status $host
done
```

#### 性能监控脚本
```bash
#!/bin/bash
# /apps/dmdbms/scripts/performance_monitor.sh

DM_HOST="*************:5236"
METRICS_FILE="/var/log/dm_metrics.log"
DISQL_PATH="/apps/dmdbms/bin/disql"

collect_metrics() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 收集会话信息
    local session_count=$($DISQL_PATH SYSDBA/SYSDBA@$DM_HOST -e "SELECT COUNT(*) FROM V\$SESSIONS;" 2>/dev/null | grep -v "COUNT" | grep -v "^$" | head -1)
    echo "[$timestamp] 当前会话数: $session_count" >> $METRICS_FILE

    # 收集锁信息
    local lock_count=$($DISQL_PATH SYSDBA/SYSDBA@$DM_HOST -e "SELECT COUNT(*) FROM V\$LOCK;" 2>/dev/null | grep -v "COUNT" | grep -v "^$" | head -1)
    echo "[$timestamp] 当前锁数量: $lock_count" >> $METRICS_FILE

    # 收集等待事件
    local wait_events=$($DISQL_PATH SYSDBA/SYSDBA@$DM_HOST -e "
    SELECT EVENT, COUNT(*) AS CNT
    FROM V\$SESSION_WAIT
    WHERE WAIT_TIME = 0
    GROUP BY EVENT
    ORDER BY CNT DESC;
    " 2>/dev/null | head -5)

    if [ -n "$wait_events" ]; then
        echo "[$timestamp] 主要等待事件:" >> $METRICS_FILE
        echo "$wait_events" >> $METRICS_FILE
    fi

    # 收集慢SQL
    local slow_sql=$($DISQL_PATH SYSDBA/SYSDBA@$DM_HOST -e "
    SELECT SQL_TEXT, ELAPSED_TIME
    FROM V\$SQL
    WHERE ELAPSED_TIME > 1000000
    ORDER BY ELAPSED_TIME DESC;
    " 2>/dev/null | head -3)

    if [ -n "$slow_sql" ]; then
        echo "[$timestamp] 慢SQL查询:" >> $METRICS_FILE
        echo "$slow_sql" >> $METRICS_FILE
    fi
}

collect_metrics
```

### 5.2 备份恢复策略

#### 全量备份脚本
```bash
#!/bin/bash
# /apps/dmdbms/scripts/backup_full.sh

BACKUP_DIR="/backup/dmdb"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_PATH="$BACKUP_DIR/full_$DATE"
DM_HOST="*************:5236"
DISQL_PATH="/apps/dmdbms/bin/disql"
DMRMAN_PATH="/apps/dmdbms/bin/dmrman"

# 创建备份目录
mkdir -p $BACKUP_PATH

echo "$(date): 开始全量备份到 $BACKUP_PATH"

# 使用RMAN进行备份
$DMRMAN_PATH CTLSTMT="
CONNECT SYSDBA/SYSDBA@$DM_HOST;
BACKUP DATABASE FULL TO '$BACKUP_PATH' COMPRESSED;
EXIT;
"

if [ $? -eq 0 ]; then
    echo "$(date): 全量备份完成"

    # 压缩备份文件
    cd $BACKUP_DIR
    tar -czf "full_$DATE.tar.gz" "full_$DATE"
    rm -rf "full_$DATE"

    echo "$(date): 备份文件压缩完成: full_$DATE.tar.gz"
    echo "$(date): 备份文件大小: $(du -h full_$DATE.tar.gz | cut -f1)"
else
    echo "$(date): 全量备份失败"
    exit 1
fi

# 清理过期备份（保留7天）
find $BACKUP_DIR -name "full_*.tar.gz" -mtime +7 -delete
echo "$(date): 清理过期备份文件完成"
```

#### 增量备份脚本
```bash
#!/bin/bash
# /apps/dmdbms/scripts/backup_incremental.sh

BACKUP_DIR="/backup/dmdb/incremental"
DATE=$(date +%Y%m%d_%H%M%S)
DM_HOST="*************:5236"
DMRMAN_PATH="/apps/dmdbms/bin/dmrman"

mkdir -p $BACKUP_DIR

echo "$(date): 开始增量备份"

# 执行增量备份
$DMRMAN_PATH CTLSTMT="
CONNECT SYSDBA/SYSDBA@$DM_HOST;
BACKUP DATABASE INCREMENTAL TO '$BACKUP_DIR/inc_$DATE' COMPRESSED;
EXIT;
"

if [ $? -eq 0 ]; then
    echo "$(date): 增量备份完成"
else
    echo "$(date): 增量备份失败"
    exit 1
fi
```

### 5.3 故障处理与恢复

#### 主库故障切换
```bash
#!/bin/bash
# /apps/dmdbms/scripts/failover.sh

PRIMARY_HOST="*************:5236"
STANDBY_HOST="*************:5236"
DISQL_PATH="/apps/dmdbms/bin/disql"

echo "$(date): 开始主库故障切换"

# 检查主库状态
if timeout 10 $DISQL_PATH SYSDBA/SYSDBA@$PRIMARY_HOST -e "SELECT 1 FROM DUAL;" >/dev/null 2>&1; then
    echo "$(date): 主库仍然可用，取消切换"
    exit 0
fi

echo "$(date): 主库不可用，开始切换到备库"

# 在备库执行切换
$DISQL_PATH SYSDBA/SYSDBA@$STANDBY_HOST << 'EOF'
-- 切换为主库
ALTER DATABASE RECOVER MANAGED STANDBY DATABASE FINISH;
ALTER DATABASE ACTIVATE STANDBY DATABASE;

-- 验证切换结果
SELECT DATABASE_ROLE FROM V$DATABASE;
EXIT;
EOF

if [ $? -eq 0 ]; then
    echo "$(date): 故障切换完成，新主库: $STANDBY_HOST"

    # 更新应用配置（这里需要根据实际情况调整）
    # update_app_config $STANDBY_HOST

    # 发送通知
    echo "达梦数据库故障切换完成，新主库: $STANDBY_HOST" | mail -s "数据库故障切换通知" <EMAIL>
else
    echo "$(date): 故障切换失败"
    exit 1
fi
```

## 六、常见问题与排查

### 6.1 安装问题

#### 依赖包缺失
```bash
# 错误信息：error while loading shared libraries
# 解决方案：
yum install -y libaio libaio-devel
yum install -y compat-libstdc++-33

# 验证依赖
ldd /apps/dmdbms/bin/dmserver
```

#### 权限问题
```bash
# 错误信息：Permission denied
# 解决方案：
chown -R dmdba:dmdba /apps/dmdbms
chmod 755 /apps/dmdbms/bin/*
```

### 6.2 运行时问题

#### 数据库启动失败
```bash
# 检查错误日志
tail -f /apps/dmdbms/data/DMSERVER/log/dm.log

# 常见原因：
1. 端口被占用
netstat -tlnp | grep 5236

2. 内存不足
free -h
cat /proc/meminfo

3. 磁盘空间不足
df -h /apps/dmdbms
```

#### 连接数过多
```sql
-- 查看当前连接数
SELECT COUNT(*) FROM V$SESSIONS;

-- 查看最大连接数
SELECT PARA_VALUE FROM V$DM_INI WHERE PARA_NAME = 'MAX_SESSIONS';

-- 增加最大连接数
ALTER SYSTEM SET MAX_SESSIONS = 3000;
```

### 6.3 性能问题

#### 查询性能慢
```sql
-- 查看执行计划
EXPLAIN SELECT * FROM table_name WHERE condition;

-- 查看统计信息
SELECT * FROM USER_TAB_STATISTICS WHERE TABLE_NAME = 'TABLE_NAME';

-- 更新统计信息
ANALYZE TABLE table_name COMPUTE STATISTICS;

-- 创建索引
CREATE INDEX idx_name ON table_name(column_name);
```

#### 锁等待问题
```sql
-- 查看锁信息
SELECT * FROM V$LOCK WHERE BLOCKED = 1;

-- 查看会话等待
SELECT * FROM V$SESSION_WAIT WHERE WAIT_TIME = 0;

-- 杀死阻塞会话
ALTER SYSTEM KILL SESSION 'session_id';
```

---

## 七、参考资料与最佳实践

### 7.1 官方文档
- [达梦数据库官方文档](https://eco.dameng.com/document/)
- [达梦数据库安装指南](https://eco.dameng.com/docs/zh-cn/start/install-dm-linux.html)
- [达梦数据库管理指南](https://eco.dameng.com/docs/zh-cn/admin/)

### 7.2 工业场景最佳实践
- **数据安全**：定期备份，主从复制，事务日志保护
- **高可用性**：自动故障切换，读写分离，负载均衡
- **性能优化**：合理索引，统计信息维护，SQL优化
- **运维管理**：监控告警，自动化脚本，标准化流程

---

**本指南将根据实际部署经验和达梦数据库版本更新持续完善，确保在工业场景下的最佳实践。**
