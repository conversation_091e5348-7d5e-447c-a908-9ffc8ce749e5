# MongoDB 4.x 工业场景高可用部署指南
## 银河麒麟+海光CPU（无AVX）环境

> **工业场景特化**：本指南专为工业环境设计，重点关注高可用性、数据安全和系统稳定性，优先保障业务连续性。

## 一、环境规划与要求

### 1.1 硬件配置要求

| 组件 | 最小配置 | 推荐配置 | 工业场景配置 |
|------|----------|----------|--------------|
| CPU | 4核心 | 8核心 | 16核心（海光CPU） |
| 内存 | 8GB | 16GB | 32GB+ |
| 存储 | 50GB HDD | 200GB SSD | 500GB+ NVMe SSD |
| 网络 | 百兆 | 千兆 | 万兆（集群间） |

### 1.2 系统环境要求
- **操作系统**：银河麒麟高级服务器版 V10 SP1/SP2
- **CPU架构**：海光（Hygon）系列，x86_64，不支持AVX指令集
- **文件系统**：推荐XFS，支持大文件和高并发
- **网络端口**：27017（主服务）、27018（分片）、27019（配置服务器）

### 1.3 高可用架构设计

#### 副本集架构（推荐）
```mermaid
graph TD
    A[应用层] --> B[负载均衡器]
    B --> C[Primary Node<br/>mongodb-01:27017]
    B --> D[Secondary Node<br/>mongodb-02:27017]
    B --> E[Secondary Node<br/>mongodb-03:27017]

    C -.->|复制| D
    C -.->|复制| E
    D -.->|心跳| C
    E -.->|心跳| C

    F[监控系统] --> C
    F --> D
    F --> E
```

#### 服务器规划
| 主机名 | IP地址 | 角色 | 优先级 | 配置 |
|--------|--------|------|--------|------|
| mongodb-01 | ************* | Primary | 2 | 32GB内存，500GB SSD |
| mongodb-02 | ************* | Secondary | 1 | 32GB内存，500GB SSD |
| mongodb-03 | ************* | Secondary | 1 | 32GB内存，500GB SSD |

## 二、依赖环境准备

### 2.1 系统依赖安装

#### 编译工具链
```bash
# 安装基础编译工具
yum groupinstall -y "Development Tools"
yum install -y gcc gcc-c++ make cmake

# 安装Python环境
yum install -y python3 python3-pip python3-devel

# 安装构建工具
pip3 install scons==4.4.0

# 安装系统库
yum install -y openssl-devel libcurl-devel liblzma-devel \
               zlib-devel readline-devel ncurses-devel \
               libaio-devel numactl-devel
```

#### 依赖版本验证
```bash
# 验证关键依赖版本
gcc --version          # 需要 >= 7.0
python3 --version      # 需要 >= 3.7
scons --version        # 需要 >= 4.0
openssl version        # 需要 >= 1.1.1
```

### 2.2 用户和目录准备

#### 创建MongoDB用户
```bash
# 创建专用用户组和用户
groupadd -g 1001 mongodb
useradd -u 1001 -g mongodb -r -s /bin/false -d /var/lib/mongodb mongodb

# 创建必要目录
mkdir -p /apps/mongodb/{bin,conf,logs}
mkdir -p /data/mongodb/{data,log,backup}
mkdir -p /var/run/mongodb

# 设置目录权限
chown -R mongodb:mongodb /apps/mongodb /data/mongodb /var/run/mongodb
chmod 755 /apps/mongodb /data/mongodb
chmod 750 /var/run/mongodb
```

## 三、源码获取与编译

### 3.1 获取无AVX版本源码

#### 方案一：使用预处理版本（推荐）
```bash
# 克隆无AVX补丁版MongoDB源码
cd /apps/software
git clone --recurse-submodules https://github.com/GermanAizek/mongodb-without-avx.git
cd mongodb-without-avx/mongo

# 选择优化级别补丁（工业场景推荐O2）
patch -p1 < ../o2_patch.diff
```

#### 方案二：官方源码手动处理
```bash
# 下载官方源码
wget https://github.com/mongodb/mongo/archive/r4.4.18.tar.gz
tar -zxvf r4.4.18.tar.gz
cd mongo-r4.4.18

# 手动禁用AVX指令
sed -i 's/-mavx//g' SConstruct
sed -i 's/-mavx2//g' SConstruct
sed -i 's/__AVX__/DISABLED_AVX/g' src/third_party/wiredtiger/src/include/gcc.h
```

### 3.2 编译环境配置

#### Python依赖安装
```bash
# 安装编译所需Python包
pip3 install -r etc/pip/compile-requirements.txt

# 如果网络受限，可离线安装
pip3 install --no-index --find-links /apps/software/python-packages -r etc/pip/compile-requirements.txt
```

#### 编译参数优化
```bash
# 设置编译环境变量
export CC=gcc
export CXX=g++
export CFLAGS="-O2 -march=x86-64 -mtune=generic"
export CXXFLAGS="-O2 -march=x86-64 -mtune=generic"

# 针对海光CPU优化
export PORTABLE=1
export USE_SSE=OFF
export USE_AVX=OFF
```

### 3.3 编译安装

#### 编译MongoDB服务端
```bash
# 编译mongod（根据内存情况调整并发数）
python3 buildscripts/scons.py \
    --disable-warnings-as-errors \
    --opt=on \
    --allocator=system \
    --use-system-zlib \
    --use-system-pcre \
    -j$(nproc) \
    install-mongod

# 如果内存不足，使用单线程编译
python3 buildscripts/scons.py \
    --disable-warnings-as-errors \
    --opt=on \
    -j1 \
    install-mongod
```

#### 安装到目标目录
```bash
# 安装到/apps/mongodb
python3 buildscripts/scons.py \
    DESTDIR=/apps/mongodb \
    --prefix=/usr/local \
    install-mongod

# 创建符号链接
ln -sf /apps/mongodb/usr/local/bin/* /apps/mongodb/bin/

# 验证安装
/apps/mongodb/bin/mongod --version
```

## 四、高可用集群配置

### 4.1 配置文件模板

#### 主节点配置（mongodb-01）
```yaml
# /apps/mongodb/conf/mongod.conf
systemLog:
  destination: file
  path: /data/mongodb/log/mongod.log
  logAppend: true
  logRotate: reopen
  timeStampFormat: iso8601-local

storage:
  dbPath: /data/mongodb/data
  journal:
    enabled: true
    commitIntervalMs: 100  # 工业场景优化：更频繁的日志提交
  wiredTiger:
    engineConfig:
      cacheSizeGB: 16  # 根据实际内存调整
      journalCompressor: snappy
      directoryForIndexes: true
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

processManagement:
  fork: true
  pidFilePath: /var/run/mongodb/mongod.pid
  timeZoneInfo: /usr/share/zoneinfo

net:
  bindIp: 0.0.0.0
  port: 27017
  maxIncomingConnections: 2000
  wireObjectCheck: true
  ipv6: false

replication:
  replSetName: "rs0"
  oplogSizeMB: 10240  # 10GB oplog，保障数据安全

# 工业场景安全配置
security:
  authorization: enabled
  keyFile: /apps/mongodb/conf/mongodb-keyfile

# 性能优化配置
operationProfiling:
  slowOpThresholdMs: 100
  mode: slowOp

# 工业场景参数优化
setParameter:
  enableLocalhostAuthBypass: false
  authenticationMechanisms: SCRAM-SHA-1,SCRAM-SHA-256
  maxLogSizeKB: 10240
  logLevel: 1
  cursorTimeoutMillis: 600000  # 10分钟游标超时
  notablescan: false  # 允许全表扫描（根据业务需求调整）
```

#### 从节点配置差异
```yaml
# mongodb-02 和 mongodb-03 的差异配置
net:
  bindIp: 0.0.0.0
  port: 27017

# 从节点可以设置更大的读取偏好延迟容忍度
replication:
  replSetName: "rs0"
  secondaryDelaySecs: 0  # 可设置延迟从节点用于数据保护
```

### 4.2 安全配置

#### 创建密钥文件
```bash
# 生成副本集密钥文件
openssl rand -base64 756 > /apps/mongodb/conf/mongodb-keyfile
chmod 400 /apps/mongodb/conf/mongodb-keyfile
chown mongodb:mongodb /apps/mongodb/conf/mongodb-keyfile

# 将密钥文件复制到所有节点
scp /apps/mongodb/conf/mongodb-keyfile mongodb-02:/apps/mongodb/conf/
scp /apps/mongodb/conf/mongodb-keyfile mongodb-03:/apps/mongodb/conf/
```

#### 创建管理用户
```javascript
// 在主节点执行
use admin
db.createUser({
  user: "admin",
  pwd: "your_strong_password_here",
  roles: [
    { role: "userAdminAnyDatabase", db: "admin" },
    { role: "readWriteAnyDatabase", db: "admin" },
    { role: "dbAdminAnyDatabase", db: "admin" },
    { role: "clusterAdmin", db: "admin" }
  ]
})

// 创建监控用户
db.createUser({
  user: "monitor",
  pwd: "monitor_password_here",
  roles: [
    { role: "clusterMonitor", db: "admin" },
    { role: "read", db: "local" }
  ]
})
```

### 4.3 systemd服务配置

#### 创建服务文件
```bash
# 创建systemd服务文件
cat > /etc/systemd/system/mongod.service << 'EOF'
[Unit]
Description=MongoDB Database Server
Documentation=https://docs.mongodb.org/manual
After=network-online.target
Wants=network-online.target

[Service]
User=mongodb
Group=mongodb
Type=forking
PIDFile=/var/run/mongodb/mongod.pid
ExecStart=/apps/mongodb/bin/mongod --config /apps/mongodb/conf/mongod.conf
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mongod
KillMode=mixed
TimeoutStopSec=30
LimitNOFILE=64000
LimitNPROC=64000

[Install]
WantedBy=multi-user.target
EOF

# 重载systemd配置
systemctl daemon-reload
systemctl enable mongod
```

## 五、集群部署与初始化

### 5.1 启动服务

#### 启动所有节点
```bash
# 在每个节点上启动MongoDB服务
systemctl start mongod
systemctl status mongod

# 验证服务启动
netstat -tlnp | grep 27017
ps aux | grep mongod
```

#### 检查日志
```bash
# 查看启动日志
tail -f /data/mongodb/log/mongod.log

# 检查是否有错误
grep -i error /data/mongodb/log/mongod.log
grep -i warning /data/mongodb/log/mongod.log
```

### 5.2 副本集初始化

#### 连接主节点并初始化
```javascript
// 连接到主节点
mongo --host mongodb-01:27017

// 初始化副本集
rs.initiate({
  _id: "rs0",
  members: [
    {
      _id: 0,
      host: "mongodb-01:27017",
      priority: 2,  // 主节点优先级最高
      tags: { "role": "primary", "datacenter": "dc1" }
    },
    {
      _id: 1,
      host: "mongodb-02:27017",
      priority: 1,
      tags: { "role": "secondary", "datacenter": "dc1" }
    },
    {
      _id: 2,
      host: "mongodb-03:27017",
      priority: 1,
      tags: { "role": "secondary", "datacenter": "dc1" }
    }
  ],
  settings: {
    chainingAllowed: false,  // 禁止链式复制，提高数据一致性
    heartbeatIntervalMillis: 2000,  // 心跳间隔2秒
    heartbeatTimeoutSecs: 10,  // 心跳超时10秒
    electionTimeoutMillis: 10000,  // 选举超时10秒
    catchUpTimeoutMillis: 60000,  // 追赶超时60秒
    getLastErrorModes: {
      "majority": { "datacenter": 1 }
    }
  }
})
```

#### 验证副本集状态
```javascript
// 检查副本集状态
rs.status()

// 检查副本集配置
rs.conf()

// 检查复制延迟
rs.printSlaveReplicationInfo()

// 检查oplog状态
db.oplog.rs.stats()
```

### 5.3 读写分离配置

#### 设置读偏好
```javascript
// 配置读偏好策略
db.getMongo().setReadPref("secondaryPreferred", [
  { "role": "secondary" }
])

// 为不同业务场景配置不同读策略
// 实时性要求高的业务读主库
db.collection.find().readPref("primary")

// 报表和分析业务读从库
db.collection.find().readPref("secondary")

// 就近读取
db.collection.find().readPref("nearest")
```

#### 应用连接字符串
```bash
# 高可用连接字符串
********************************************************************************************************************************************************************************************

# 参数说明：
# replicaSet=rs0: 副本集名称
# readPreference=secondaryPreferred: 优先读从库
# maxPoolSize=50: 最大连接池大小
# retryWrites=true: 启用写重试
# w=majority: 写关注级别为多数
# wtimeoutMS=5000: 写超时5秒
```

## 六、监控与运维管理

### 6.1 健康检查脚本

#### 副本集状态监控
```bash
#!/bin/bash
# /apps/mongodb/scripts/health_check.sh

MONGO_HOSTS=("mongodb-01:27017" "mongodb-02:27017" "mongodb-03:27017")
LOG_FILE="/var/log/mongodb_health.log"
ALERT_EMAIL="<EMAIL>"

check_replica_status() {
    local host=$1
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    echo "[$timestamp] 检查节点 $host" >> $LOG_FILE

    # 检查节点连通性
    if ! timeout 5 mongo --host $host --eval "db.runCommand('ping')" --quiet >/dev/null 2>&1; then
        echo "[$timestamp] 严重告警 - 节点 $host 不可达" >> $LOG_FILE
        send_alert "MongoDB节点不可达" "节点 $host 无法连接"
        return 1
    fi

    # 检查副本集状态
    local state=$(mongo --host $host --eval "rs.status().myState" --quiet 2>/dev/null)
    echo "[$timestamp] 节点 $host 状态: $state" >> $LOG_FILE

    case $state in
        1) echo "[$timestamp] 节点 $host 是主节点" >> $LOG_FILE ;;
        2) echo "[$timestamp] 节点 $host 是从节点" >> $LOG_FILE ;;
        7) echo "[$timestamp] 节点 $host 是仲裁节点" >> $LOG_FILE ;;
        *)
            echo "[$timestamp] 警告 - 节点 $host 状态异常: $state" >> $LOG_FILE
            send_alert "MongoDB节点状态异常" "节点 $host 状态: $state"
            ;;
    esac

    # 检查复制延迟
    if [ "$state" = "2" ]; then
        local lag=$(mongo --host $host --eval "rs.status().members.find(m => m.self).optimeDate" --quiet 2>/dev/null)
        local primary_time=$(mongo --host mongodb-01:27017 --eval "rs.status().members.find(m => m.state === 1).optimeDate" --quiet 2>/dev/null)
        # 这里可以添加延迟计算和告警逻辑
    fi
}

send_alert() {
    local subject=$1
    local message=$2
    echo "$message" | mail -s "$subject" $ALERT_EMAIL
}

# 检查所有节点
for host in "${MONGO_HOSTS[@]}"; do
    check_replica_status $host
done

# 检查副本集整体状态
PRIMARY_COUNT=$(mongo --host mongodb-01:27017 --eval "rs.status().members.filter(m => m.state === 1).length" --quiet 2>/dev/null || echo "0")
if [ "$PRIMARY_COUNT" -ne 1 ]; then
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] 严重告警 - 主节点数量异常: $PRIMARY_COUNT" >> $LOG_FILE
    send_alert "MongoDB主节点异常" "当前主节点数量: $PRIMARY_COUNT"
fi

# 检查磁盘空间
for host in "${MONGO_HOSTS[@]}"; do
    disk_usage=$(ssh ${host%:*} "df -h /data/mongodb | tail -1 | awk '{print \$5}' | sed 's/%//'")
    if [ "$disk_usage" -gt 80 ]; then
        echo "[$timestamp] 警告 - 节点 ${host%:*} 磁盘使用率: ${disk_usage}%" >> $LOG_FILE
        send_alert "MongoDB磁盘空间告警" "节点 ${host%:*} 磁盘使用率: ${disk_usage}%"
    fi
done
```

#### 性能监控脚本
```bash
#!/bin/bash
# /apps/mongodb/scripts/performance_monitor.sh

MONGO_HOST="mongodb-01:27017"
METRICS_FILE="/var/log/mongodb_metrics.log"

collect_metrics() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 收集服务器状态
    mongo --host $MONGO_HOST --eval "
    var status = db.serverStatus();
    print('[$timestamp] 连接数: ' + status.connections.current + '/' + status.connections.available);
    print('[$timestamp] 内存使用: ' + Math.round(status.mem.resident) + 'MB');
    print('[$timestamp] 网络流量: 入' + Math.round(status.network.bytesIn/1024/1024) + 'MB 出' + Math.round(status.network.bytesOut/1024/1024) + 'MB');
    print('[$timestamp] 操作计数: 查询' + status.opcounters.query + ' 插入' + status.opcounters.insert + ' 更新' + status.opcounters.update + ' 删除' + status.opcounters.delete);
    " --quiet >> $METRICS_FILE

    # 收集慢查询
    mongo --host $MONGO_HOST --eval "
    db.setProfilingLevel(1, {slowms: 100});
    var slowOps = db.system.profile.find().sort({ts: -1}).limit(5);
    slowOps.forEach(function(op) {
        if (op.millis > 100) {
            print('[$timestamp] 慢查询: ' + op.ns + ' 耗时: ' + op.millis + 'ms');
        }
    });
    " --quiet >> $METRICS_FILE
}

# 定时收集指标
collect_metrics

# 清理旧日志（保留7天）
find /var/log -name "mongodb_*.log" -mtime +7 -delete
```

### 6.2 自动化备份策略

#### 全量备份脚本
```bash
#!/bin/bash
# /apps/mongodb/scripts/backup_full.sh

BACKUP_DIR="/backup/mongodb"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_PATH="$BACKUP_DIR/full_$DATE"
MONGO_HOST="mongodb-01:27017"
RETENTION_DAYS=7

# 创建备份目录
mkdir -p $BACKUP_PATH

# 执行全量备份
echo "$(date): 开始全量备份到 $BACKUP_PATH"
mongodump --host $MONGO_HOST \
          --username admin \
          --password "your_password" \
          --authenticationDatabase admin \
          --oplog \
          --gzip \
          --out $BACKUP_PATH

if [ $? -eq 0 ]; then
    echo "$(date): 全量备份完成"

    # 压缩备份文件
    cd $BACKUP_DIR
    tar -czf "full_$DATE.tar.gz" "full_$DATE"
    rm -rf "full_$DATE"

    # 验证备份文件
    if [ -f "full_$DATE.tar.gz" ]; then
        echo "$(date): 备份文件创建成功: full_$DATE.tar.gz"
        echo "$(date): 备份文件大小: $(du -h full_$DATE.tar.gz | cut -f1)"
    fi
else
    echo "$(date): 全量备份失败"
    exit 1
fi

# 清理过期备份
find $BACKUP_DIR -name "full_*.tar.gz" -mtime +$RETENTION_DAYS -delete
echo "$(date): 清理 $RETENTION_DAYS 天前的备份文件"
```

#### 增量备份脚本
```bash
#!/bin/bash
# /apps/mongodb/scripts/backup_incremental.sh

BACKUP_DIR="/backup/mongodb/incremental"
DATE=$(date +%Y%m%d_%H%M%S)
MONGO_HOST="mongodb-01:27017"
LAST_BACKUP_FILE="/backup/mongodb/last_oplog_time.txt"

mkdir -p $BACKUP_DIR

# 获取上次备份的oplog时间
if [ -f $LAST_BACKUP_FILE ]; then
    LAST_OPLOG_TIME=$(cat $LAST_BACKUP_FILE)
    echo "$(date): 从 $LAST_OPLOG_TIME 开始增量备份"

    # 执行增量备份
    mongodump --host $MONGO_HOST \
              --username admin \
              --password "your_password" \
              --authenticationDatabase admin \
              --db local \
              --collection oplog.rs \
              --query "{ts: {\$gt: Timestamp($LAST_OPLOG_TIME, 0)}}" \
              --gzip \
              --out "$BACKUP_DIR/inc_$DATE"
else
    echo "$(date): 首次增量备份，备份所有oplog"
    mongodump --host $MONGO_HOST \
              --username admin \
              --password "your_password" \
              --authenticationDatabase admin \
              --db local \
              --collection oplog.rs \
              --gzip \
              --out "$BACKUP_DIR/inc_$DATE"
fi

# 记录当前oplog时间
mongo --host $MONGO_HOST \
      --username admin \
      --password "your_password" \
      --authenticationDatabase admin \
      --eval "db.runCommand('isMaster').lastWrite.lastWriteDate.getTime()" \
      --quiet > $LAST_BACKUP_FILE

echo "$(date): 增量备份完成"
```

### 6.3 定时任务配置

#### 设置crontab
```bash
# 编辑crontab
crontab -e

# 添加以下任务
# 每天凌晨2点执行全量备份
0 2 * * * /apps/mongodb/scripts/backup_full.sh >> /var/log/mongodb_backup.log 2>&1

# 每小时执行增量备份
0 * * * * /apps/mongodb/scripts/backup_incremental.sh >> /var/log/mongodb_backup.log 2>&1

# 每5分钟执行健康检查
*/5 * * * * /apps/mongodb/scripts/health_check.sh

# 每分钟收集性能指标
* * * * * /apps/mongodb/scripts/performance_monitor.sh
```

## 七、故障处理与恢复

### 7.1 常见故障场景

#### 主节点故障处理
```bash
# 1. 检查主节点状态
mongo --host mongodb-01:27017 --eval "rs.status()"

# 2. 如果主节点不可达，检查从节点状态
mongo --host mongodb-02:27017 --eval "rs.status()"

# 3. 观察选举过程
mongo --host mongodb-02:27017 --eval "
while(true) {
    var status = rs.status();
    var primary = status.members.find(m => m.state === 1);
    if (primary) {
        print(new Date() + ': 当前主节点是 ' + primary.name);
    } else {
        print(new Date() + ': 正在进行主节点选举...');
    }
    sleep(2000);
}
"

# 4. 如果选举失败，手动触发选举
mongo --host mongodb-02:27017 --eval "rs.stepDown()"
```

#### 从节点故障处理
```bash
# 1. 检查从节点状态
mongo --host mongodb-03:27017 --eval "rs.status()"

# 2. 如果从节点落后太多，重新同步
mongo --host mongodb-03:27017 --eval "
db.adminCommand({
    'replSetSyncFrom': 'mongodb-01:27017'
})
"

# 3. 如果需要重建从节点
systemctl stop mongod
rm -rf /data/mongodb/data/*
systemctl start mongod

# 4. 从节点会自动开始初始同步
```

#### 数据恢复操作
```bash
# 1. 从全量备份恢复
tar -xzf /backup/mongodb/full_20240101_020000.tar.gz
mongorestore --host mongodb-01:27017 \
             --username admin \
             --password "your_password" \
             --authenticationDatabase admin \
             --oplogReplay \
             --gzip \
             full_20240101_020000/

# 2. 应用增量备份
mongorestore --host mongodb-01:27017 \
             --username admin \
             --password "your_password" \
             --authenticationDatabase admin \
             --oplogReplay \
             --gzip \
             incremental/inc_20240101_030000/

# 3. 验证数据完整性
mongo --host mongodb-01:27017 --eval "
db.runCommand({dbStats: 1});
db.collection.count();
"
```

### 7.2 性能优化

#### 索引优化
```javascript
// 查看慢查询
db.setProfilingLevel(2, {slowms: 100})
db.system.profile.find().sort({ts: -1}).limit(10)

// 分析查询计划
db.collection.find({field: "value"}).explain("executionStats")

// 创建复合索引
db.collection.createIndex({field1: 1, field2: -1}, {background: true})

// 查看索引使用情况
db.collection.getIndexes()
db.collection.stats().indexSizes
```

#### 内存优化
```bash
# 调整WiredTiger缓存大小
mongo --eval "
db.adminCommand({
    setParameter: 1,
    wiredTigerEngineRuntimeConfig: 'cache_size=16GB'
})
"

# 检查内存使用
mongo --eval "db.serverStatus().wiredTiger.cache"
```

### 7.3 故障自愈脚本

#### 自动重启脚本
```bash
#!/bin/bash
# /apps/mongodb/scripts/auto_recovery.sh

MONGO_HOST="mongodb-01:27017"
MAX_RETRIES=3
RETRY_COUNT=0

check_and_restart() {
    local host=$1
    local hostname=${host%:*}

    # 检查MongoDB进程
    if ! ssh $hostname "pgrep mongod" >/dev/null; then
        echo "$(date): MongoDB进程在 $hostname 上未运行，尝试重启"

        ssh $hostname "systemctl start mongod"
        sleep 10

        # 验证重启是否成功
        if ssh $hostname "pgrep mongod" >/dev/null; then
            echo "$(date): MongoDB在 $hostname 上重启成功"
            return 0
        else
            echo "$(date): MongoDB在 $hostname 上重启失败"
            return 1
        fi
    fi

    # 检查MongoDB连接
    if ! timeout 5 mongo --host $host --eval "db.runCommand('ping')" --quiet >/dev/null 2>&1; then
        echo "$(date): MongoDB在 $host 上无法连接，尝试重启"

        ssh $hostname "systemctl restart mongod"
        sleep 15

        if timeout 5 mongo --host $host --eval "db.runCommand('ping')" --quiet >/dev/null 2>&1; then
            echo "$(date): MongoDB在 $host 上重启后连接成功"
            return 0
        else
            echo "$(date): MongoDB在 $host 上重启后仍无法连接"
            return 1
        fi
    fi

    return 0
}

# 检查所有节点
for host in "mongodb-01:27017" "mongodb-02:27017" "mongodb-03:27017"; do
    if ! check_and_restart $host; then
        RETRY_COUNT=$((RETRY_COUNT + 1))
        if [ $RETRY_COUNT -ge $MAX_RETRIES ]; then
            echo "$(date): 达到最大重试次数，发送告警"
            echo "MongoDB节点 $host 故障，需要人工干预" | mail -s "MongoDB严重故障" <EMAIL>
            break
        fi
    fi
done
```

## 八、常见问题与排查

### 8.1 编译安装问题

#### AVX指令集错误
```bash
# 错误信息：Illegal instruction (core dumped)
# 解决方案：
1. 确认使用了无AVX补丁的源码
2. 检查编译参数是否正确设置
3. 验证CPU是否支持编译的指令集

# 验证方法：
lscpu | grep -i avx
strings /apps/mongodb/bin/mongod | grep -i avx
```

#### 编译依赖问题
```bash
# 错误信息：fatal error: openssl/ssl.h: No such file or directory
# 解决方案：
yum install -y openssl-devel

# 错误信息：Python.h: No such file or directory
# 解决方案：
yum install -y python3-devel

# 错误信息：fatal error: curl/curl.h: No such file or directory
# 解决方案：
yum install -y libcurl-devel
```

#### 内存不足问题
```bash
# 错误信息：virtual memory exhausted: Cannot allocate memory
# 解决方案：
1. 增加swap空间
dd if=/dev/zero of=/swapfile bs=1G count=4
chmod 600 /swapfile
mkswap /swapfile
swapon /swapfile

2. 使用单线程编译
python3 buildscripts/scons.py -j1 install-mongod
```

### 8.2 运行时问题

#### 副本集初始化失败
```bash
# 错误信息：replSetInitiate failed
# 排查步骤：
1. 检查网络连通性
ping mongodb-02
ping mongodb-03

2. 检查端口开放
telnet mongodb-02 27017
telnet mongodb-03 27017

3. 检查主机名解析
nslookup mongodb-02
nslookup mongodb-03

4. 检查配置文件
grep -E "replSetName|bindIp" /apps/mongodb/conf/mongod.conf
```

#### 连接数过多
```bash
# 错误信息：too many connections
# 解决方案：
1. 增加最大连接数
mongo --eval "db.adminCommand({setParameter: 1, maxIncomingConnections: 5000})"

2. 优化应用连接池
# 在应用中设置合理的连接池大小

3. 检查连接泄露
mongo --eval "db.serverStatus().connections"
```

#### 磁盘空间不足
```bash
# 错误信息：No space left on device
# 解决方案：
1. 清理日志文件
mongo --eval "db.runCommand({logRotate: 1})"

2. 压缩数据文件
mongo --eval "db.runCommand({compact: 'collection_name'})"

3. 清理oplog（谨慎操作）
mongo --eval "
use local;
var lastOplog = db.oplog.rs.find().sort({ts: -1}).limit(1).next();
print('最新oplog时间: ' + lastOplog.ts);
"
```

### 8.3 性能问题

#### 查询性能慢
```bash
# 排查步骤：
1. 启用慢查询日志
mongo --eval "db.setProfilingLevel(1, {slowms: 100})"

2. 分析慢查询
mongo --eval "db.system.profile.find().sort({ts: -1}).limit(10)"

3. 检查索引使用
mongo --eval "db.collection.find({field: 'value'}).explain('executionStats')"

4. 创建合适的索引
mongo --eval "db.collection.createIndex({field: 1}, {background: true})"
```

#### 内存使用过高
```bash
# 排查步骤：
1. 检查内存使用情况
mongo --eval "db.serverStatus().mem"

2. 调整WiredTiger缓存
mongo --eval "
db.adminCommand({
    setParameter: 1,
    wiredTigerEngineRuntimeConfig: 'cache_size=8GB'
})
"

3. 检查大对象
mongo --eval "
db.collection.find().sort({size: -1}).limit(10).forEach(
    function(doc) { print(Object.bsonsize(doc) + ' bytes'); }
)
"
```

---

## 九、参考资料与最佳实践

### 9.1 官方文档
- [MongoDB官方文档](https://docs.mongodb.com/)
- [MongoDB副本集部署](https://docs.mongodb.com/manual/replication/)
- [MongoDB性能优化](https://docs.mongodb.com/manual/administration/appsimization/)

### 9.2 社区资源
- [MongoDB无AVX版本](https://github.com/GermanAizek/mongodb-without-avx)
- [银河麒麟兼容性指南](https://www.kylinos.cn/)
- [海光CPU优化指南](https://www.hygon.cn/)

### 9.3 工业场景最佳实践
- **数据安全**：定期备份，多副本存储，异地容灾
- **高可用性**：自动故障转移，健康检查，监控告警
- **性能优化**：合理索引，读写分离，连接池管理
- **运维管理**：自动化脚本，标准化流程，文档维护

---

**本指南将根据实际部署经验和技术发展持续更新，确保在工业场景下的最佳实践。**