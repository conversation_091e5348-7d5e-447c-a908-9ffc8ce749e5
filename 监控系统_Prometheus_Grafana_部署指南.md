# 监控系统部署指南
## Prometheus + Grafana + AlertManager 工业场景监控方案

> **工业场景特化**：本指南专为工业环境设计，提供全方位的基础服务监控方案，确保系统稳定运行和及时故障发现。

## 一、监控架构设计

### 1.1 整体架构

```mermaid
graph TD
    A[被监控服务] --> B[Prometheus Server]
    B --> C[Grafana Dashboard]
    B --> D[AlertManager]
    D --> E[邮件告警]
    D --> F[钉钉告警]
    D --> G[短信告警]
    
    H[Node Exporter] --> B
    I[MongoDB Exporter] --> B
    J[Redis Exporter] --> B
    K[Kafka Exporter] --> B
    L[Custom Metrics] --> B
```

### 1.2 服务器规划

| 主机名 | IP地址 | 服务 | 配置要求 |
|--------|--------|------|----------|
| monitor-01 | ************* | Prometheus + AlertManager | 16GB内存，200GB SSD |
| monitor-02 | ************* | Grafana + Backup | 8GB内存，100GB SSD |

## 二、Prometheus 部署

### 2.1 安装Prometheus

#### 下载安装包
```bash
# 创建prometheus用户
useradd -r -s /bin/false prometheus

# 下载Prometheus
cd /apps/software
wget https://github.com/prometheus/prometheus/releases/download/v2.45.0/prometheus-2.45.0.linux-amd64.tar.gz
tar -zxvf prometheus-2.45.0.linux-amd64.tar.gz
mv prometheus-2.45.0.linux-amd64 /apps/prometheus

# 创建目录
mkdir -p /apps/prometheus/{data,config,rules}
mkdir -p /var/log/prometheus
chown -R prometheus:prometheus /apps/prometheus /var/log/prometheus
```

#### 配置文件
```yaml
# /apps/prometheus/config/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'industrial-cluster'
    environment: 'production'

rule_files:
  - "/apps/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - *************:9093

scrape_configs:
  # Prometheus自监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets:
        - '*************:9100'  # mongodb-01
        - '*************:9100'  # mongodb-02
        - '*************:9100'  # mongodb-03
        - '*************:9100'  # redis-01
        - '*************:9100'  # redis-02
        - '*************:9100'  # redis-03

  # MongoDB监控
  - job_name: 'mongodb'
    static_configs:
      - targets:
        - '*************:9216'
        - '*************:9216'
        - '*************:9216'

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets:
        - '*************:9121'
        - '*************:9121'
        - '*************:9121'

  # Kafka监控
  - job_name: 'kafka'
    static_configs:
      - targets:
        - '*************:9308'
        - '*************:9308'
        - '*************:9308'
```

#### 启动服务
```bash
# 创建systemd服务
cat > /etc/systemd/system/prometheus.service << 'EOF'
[Unit]
Description=Prometheus Server
After=network.target

[Service]
User=prometheus
Group=prometheus
Type=simple
ExecStart=/apps/prometheus/prometheus \
  --config.file=/apps/prometheus/config/prometheus.yml \
  --storage.tsdb.path=/apps/prometheus/data \
  --web.console.templates=/apps/prometheus/consoles \
  --web.console.libraries=/apps/prometheus/console_libraries \
  --web.listen-address=0.0.0.0:9090 \
  --web.enable-lifecycle \
  --storage.tsdb.retention.time=30d
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable prometheus
systemctl start prometheus
```

### 2.2 部署Exporters

#### Node Exporter（系统监控）
```bash
# 在每台服务器上安装
wget https://github.com/prometheus/node_exporter/releases/download/v1.6.0/node_exporter-1.6.0.linux-amd64.tar.gz
tar -zxvf node_exporter-1.6.0.linux-amd64.tar.gz
mv node_exporter-1.6.0.linux-amd64/node_exporter /usr/local/bin/

# 创建服务
cat > /etc/systemd/system/node_exporter.service << 'EOF'
[Unit]
Description=Node Exporter
After=network.target

[Service]
User=nobody
Group=nobody
Type=simple
ExecStart=/usr/local/bin/node_exporter \
  --web.listen-address=0.0.0.0:9100
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable node_exporter
systemctl start node_exporter
```

#### MongoDB Exporter
```bash
# 在MongoDB服务器上安装
wget https://github.com/percona/mongodb_exporter/releases/download/v0.39.0/mongodb_exporter-0.39.0.linux-amd64.tar.gz
tar -zxvf mongodb_exporter-0.39.0.linux-amd64.tar.gz
mv mongodb_exporter-0.39.0.linux-amd64/mongodb_exporter /usr/local/bin/

# 创建服务
cat > /etc/systemd/system/mongodb_exporter.service << 'EOF'
[Unit]
Description=MongoDB Exporter
After=network.target

[Service]
User=mongodb
Group=mongodb
Type=simple
Environment=MONGODB_URI=************************************************
ExecStart=/usr/local/bin/mongodb_exporter \
  --web.listen-address=0.0.0.0:9216 \
  --collect-all
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable mongodb_exporter
systemctl start mongodb_exporter
```

#### Redis Exporter
```bash
# 在Redis服务器上安装
wget https://github.com/oliver006/redis_exporter/releases/download/v1.52.0/redis_exporter-v1.52.0.linux-amd64.tar.gz
tar -zxvf redis_exporter-v1.52.0.linux-amd64.tar.gz
mv redis_exporter-v1.52.0.linux-amd64/redis_exporter /usr/local/bin/

# 创建服务
cat > /etc/systemd/system/redis_exporter.service << 'EOF'
[Unit]
Description=Redis Exporter
After=network.target

[Service]
User=redis
Group=redis
Type=simple
Environment=REDIS_ADDR=redis://localhost:7001
Environment=REDIS_PASSWORD=your_password
ExecStart=/usr/local/bin/redis_exporter \
  --web.listen-address=0.0.0.0:9121
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable redis_exporter
systemctl start redis_exporter
```

## 三、AlertManager 部署

### 3.1 安装配置

#### 安装AlertManager
```bash
# 下载安装
wget https://github.com/prometheus/alertmanager/releases/download/v0.25.0/alertmanager-0.25.0.linux-amd64.tar.gz
tar -zxvf alertmanager-0.25.0.linux-amd64.tar.gz
mv alertmanager-0.25.0.linux-amd64 /apps/alertmanager

# 创建目录
mkdir -p /apps/alertmanager/{data,config}
chown -R prometheus:prometheus /apps/alertmanager
```

#### 配置文件
```yaml
# /apps/alertmanager/config/alertmanager.yml
global:
  smtp_smarthost: 'smtp.company.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
  - match:
      severity: warning
    receiver: 'warning-alerts'

receivers:
- name: 'default'
  email_configs:
  - to: '<EMAIL>'
    subject: '[监控告警] {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      告警名称: {{ .Annotations.summary }}
      告警详情: {{ .Annotations.description }}
      告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      告警级别: {{ .Labels.severity }}
      {{ end }}

- name: 'critical-alerts'
  email_configs:
  - to: '<EMAIL>,<EMAIL>'
    subject: '[严重告警] {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      告警名称: {{ .Annotations.summary }}
      告警详情: {{ .Annotations.description }}
      告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      告警级别: {{ .Labels.severity }}
      {{ end }}

- name: 'warning-alerts'
  email_configs:
  - to: '<EMAIL>'
    subject: '[警告告警] {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      告警名称: {{ .Annotations.summary }}
      告警详情: {{ .Annotations.description }}
      告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      告警级别: {{ .Labels.severity }}
      {{ end }}
```

### 3.2 告警规则

#### 系统告警规则
```yaml
# /apps/prometheus/rules/system_alerts.yml
groups:
- name: system_alerts
  rules:
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "服务器CPU使用率过高"
      description: "服务器 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"

  - alert: HighMemoryUsage
    expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "服务器内存使用率过高"
      description: "服务器 {{ $labels.instance }} 内存使用率超过85%，当前值: {{ $value }}%"

  - alert: DiskSpaceUsage
    expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "磁盘空间使用率过高"
      description: "服务器 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率超过85%，当前值: {{ $value }}%"
```

#### 数据库告警规则
```yaml
# /apps/prometheus/rules/database_alerts.yml
groups:
- name: mongodb_alerts
  rules:
  - alert: MongoDBDown
    expr: up{job="mongodb"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "MongoDB服务不可用"
      description: "MongoDB实例 {{ $labels.instance }} 无法连接"

  - alert: MongoDBReplicationLag
    expr: mongodb_replset_member_optime_date{state="SECONDARY"} - on (set) group_left mongodb_replset_member_optime_date{state="PRIMARY"} > 10
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "MongoDB复制延迟过高"
      description: "MongoDB从节点 {{ $labels.instance }} 复制延迟超过10秒"

- name: redis_alerts
  rules:
  - alert: RedisDown
    expr: up{job="redis"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Redis服务不可用"
      description: "Redis实例 {{ $labels.instance }} 无法连接"

  - alert: RedisMemoryUsage
    expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Redis内存使用率过高"
      description: "Redis实例 {{ $labels.instance }} 内存使用率超过85%，当前值: {{ $value }}%"
```

## 四、Grafana 部署

### 4.1 安装Grafana

#### 安装配置
```bash
# 下载安装
wget https://dl.grafana.com/enterprise/release/grafana-enterprise-10.0.0.linux-amd64.tar.gz
tar -zxvf grafana-enterprise-10.0.0.linux-amd64.tar.gz
mv grafana-10.0.0 /apps/grafana

# 创建用户和目录
useradd -r -s /bin/false grafana
mkdir -p /apps/grafana/{data,logs,plugins}
chown -R grafana:grafana /apps/grafana
```

#### 配置文件
```ini
# /apps/grafana/conf/custom.ini
[server]
http_addr = 0.0.0.0
http_port = 3000
domain = monitor.company.com
root_url = http://monitor.company.com:3000/

[database]
type = sqlite3
path = /apps/grafana/data/grafana.db

[security]
admin_user = admin
admin_password = admin123456
secret_key = your_secret_key_here

[users]
allow_sign_up = false
default_theme = dark

[auth.anonymous]
enabled = false

[alerting]
enabled = true
execute_alerts = true

[smtp]
enabled = true
host = smtp.company.com:587
user = <EMAIL>
password = password
from_address = <EMAIL>
from_name = Grafana
```

#### 启动服务
```bash
# 创建systemd服务
cat > /etc/systemd/system/grafana.service << 'EOF'
[Unit]
Description=Grafana Server
After=network.target

[Service]
User=grafana
Group=grafana
Type=simple
WorkingDirectory=/apps/grafana
ExecStart=/apps/grafana/bin/grafana-server \
  --config=/apps/grafana/conf/custom.ini \
  --homepath=/apps/grafana
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable grafana
systemctl start grafana
```

---

**本指南提供了完整的监控系统部署方案，后续将根据实际需求添加更多监控面板和告警规则。**
